use crate::{
    app_writer::{AppR<PERSON><PERSON>, AppWriter},
    db::{CreateParams, IdParams, ListOptions, ListParams, ListResponse, Page, UpdateParams},
    dtos::menu::{MenuCreate, MenuResponse, MenuUpdate},
    services::menu::MenuService,
};
use anyhow::anyhow;
use salvo::{
    oapi::{
        endpoint,
        extract::{JsonBody, PathParam},
    },
    Depot,
};
use salvo::{Router, Writer};

pub fn router() -> Router {
    Router::with_path("menu")
        .post(create_menu)
        .put(update_menu)
        .push(Router::with_path("current").post(get_current))
        .push(Router::with_path("list").post(get_menu_list))
        .push(Router::with_path("role").post(get_menu_by_role))
        .push(
            Router::with_path("<id>")
                .get(get_menu_by_id)
                .delete(delete_menu),
        )
}

#[endpoint(tags("menu"))]
async fn get_menu_list() -> AppWriter<ListResponse<MenuResponse>> {
    let params = ListParams {
        page: Some(Page::unlimited()),
        params: None,
        options: Some(ListOptions {
            order_by: Some("order".to_string()),
            desc: Some(true),
        }),
    };
    let list = MenuService::get_list(params).await.unwrap();
    let mut data: Vec<MenuResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        data.push(tmp);
    }
    let tree = MenuResponse::array_to_tree(data);
    let res = ListResponse {
        data: tree,
        total: 0,
        page: 0,
        size: 0,
    };

    AppWriter(Ok(res))
}

#[endpoint(tags("menu"))]
async fn get_menu_by_role(req: JsonBody<IdParams>) -> AppWriter<Vec<MenuResponse>> {
    let list = MenuService::get_list_by_role(req.0.id.clone())
        .await
        .unwrap();
    let mut res: Vec<MenuResponse> = Vec::new();
    for item in list {
        let tmp = item.response();
        res.push(tmp);
    }
    AppWriter(Ok(res))
}

#[endpoint(tags("menu"), parameters(("id", description = "menu id")))]
async fn get_menu_by_id(id: PathParam<String>) -> AppWriter<MenuResponse> {
    match MenuService::get_by_id(id.0).await {
        Ok(menu) => {
            if let Some(menu) = menu {
                let res = menu.response();
                AppWriter(Ok(res))
            } else {
                AppWriter(Err(anyhow!("menu not found").into()))
            }
        }
        Err(e) => AppWriter(Err(e)),
    }
}

#[endpoint(tags("menu"))]
async fn create_menu(req: JsonBody<MenuCreate>) -> AppWriter<String> {
    let result = MenuService::create(CreateParams { data: req.0 }).await;
    AppWriter(result)
}

#[endpoint(tags("menu"))]
async fn update_menu(req: JsonBody<MenuUpdate>) -> AppResult<AppWriter<String>> {
    let result = MenuService::update(UpdateParams { data: req.0 }).await;
    Ok(AppWriter(result))
}

#[endpoint(tags("menu"), parameters(("id", description = "menu id")))]
async fn delete_menu(id: PathParam<String>) -> AppWriter<String> {
    let result = MenuService::delete(id.0).await;
    AppWriter(result)
}

#[endpoint(tags("menu"))]
async fn get_current(depot: &mut Depot) -> AppWriter<Vec<MenuResponse>> {
    let role_id = depot.get::<String>("current_role").unwrap();
    println!("current role_id: {}", role_id);
    let menu = MenuService::get_list_by_role(role_id.clone())
        .await
        .unwrap();
    let mut res = Vec::new();
    for item in menu {
        let tmp = item.response();
        res.push(tmp);
    }
    AppWriter(Ok(res))
}
