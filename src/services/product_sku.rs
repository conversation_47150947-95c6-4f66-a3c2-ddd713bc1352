use crate::utils::upload::replace_content;
use crate::{
    app_writer::AppResult,
    db::{ListParams, Page, RelateParams, WhereOptions},
    dtos::product_sku::{ProductSkuCreate, ProductSkuUpdate},
    entities::product_sku::{ProductSku, ProductSkuBmc},
    services::upload::UploadService,
};
use anyhow::anyhow;

pub struct ProductSkuService;
impl ProductSkuService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match ProductSkuBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<ProductSku>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = ProductSkuBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<ProductSku> {
        match ProductSkuBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ProductSku not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn get_by_id(id: String) -> AppResult<ProductSku> {
        match ProductSkuBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("ProductSku not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    pub async fn draft(mut req: ProductSkuCreate) -> AppResult<String> {
        req.status = "draft".to_string();
        match ProductSkuBmc::draft(req).await {
            Ok(res) => Ok(res),
            Err(e) => Err(e),
        }
    }

    pub async fn publish(mut req: ProductSkuUpdate) -> AppResult<String> {
        // 1. 如果有内容,处理内容中的图片URL
        if let Some(content) = &req.content {
            req.content = Some(replace_content(
                content.to_string(),
                "tmp/".to_string(),
                "".to_string(),
            ));
        }

        // 2. 设置状态为active
        req.status = "active".to_string();

        // 3. 更新文章
        let product_sku_id = req.id.clone();
        let res = ProductSkuBmc::update(req).await?;

        // 4. 获取相关的图片列表
        let where_options = vec![
            WhereOptions {
                var: "t_id".to_string(),
                val: product_sku_id.clone(),
            },
            WhereOptions {
                var: "file_type".to_string(),
                val: "content".to_string(),
            },
        ];
        let list_params = ListParams {
            page: Some(Page::unlimited()),
            params: Some(where_options),
            options: None,
        };
        let images = UploadService::get_list(list_params).await?;

        // 5. 循环更新图片
        for image in images {
            if image.is_temp {
                UploadService::update_upload("system".to_string(), image.id.to_string()).await?;
            }
        }

        Ok(res)
    }

    pub async fn create(req: ProductSkuCreate) -> AppResult<String> {
        match ProductSkuBmc::create(req).await {
            Ok(res) => Ok(res),
            Err(e) => Err(e),
        }
    }

    pub async fn update(req: ProductSkuUpdate) -> AppResult<String> {
        match ProductSkuBmc::update(req).await {
            Ok(res) => Ok(res),
            Err(e) => Err(e),
        }
    }

    pub async fn delete(id: String) -> AppResult<String> {
        match ProductSkuBmc::delete(id).await {
            Ok(res) => Ok(res),
            Err(e) => Err(e),
        }
    }

    pub async fn relate_upload(req: RelateParams, file_type: String) -> AppResult<String> {
        let state = format!("has_{}", file_type);
        match ProductSkuBmc::relate_to_item(req, &state).await {
            Ok(res) => Ok(res),
            Err(e) => Err(e),
        }
    }
}
