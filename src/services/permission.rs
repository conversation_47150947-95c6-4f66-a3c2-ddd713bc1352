use std::collections::HashMap;

use super::role::RoleService;
use crate::{
    app_writer::AppResult,
    db::{CreateParams, ListOptions, ListParams, UpdateParams, WhereOptions},
    dtos::permission::{PermissionCreate, PermissionUpdate},
    entities::permission::{Permission, PermissionBmc},
    utils::redis,
};
use anyhow::anyhow;

pub struct PermissionService;
impl PermissionService {
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match PermissionBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => return Ok(0),
        }
    }

    pub async fn get_list(req: ListParams) -> AppResult<Vec<Permission>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = PermissionBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    pub async fn get_list_by_role(req: String) -> AppResult<Vec<Permission>> {
        let check = match RoleService::get_by_id(req.clone()).await {
            Ok(role) => role,
            Err(e) => return Err(e),
        };
        if check.code.is_none() {
            return Err(anyhow!("Role Code is bad").into());
        }

        let options = Some(ListOptions {
            order_by: Some("order".to_string()),
            desc: Some(true),
        });

        if check.code.unwrap() == "administrator".to_string() {
            let res = PermissionBmc::get_list(0, 0, options, vec![]).await?;
            return Ok(res);
        }

        let where_options = vec![WhereOptions {
            var: "ralate".to_string(),
            val: format!("id IN {}->has_permission.out", req),
        }];
        let res = PermissionBmc::get_list(0, 0, options, where_options).await?;
        Ok(res)
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Permission>> {
        let res = PermissionBmc::get_by_query(params).await?;
        Ok(res)
    }

    pub async fn get_by_id(id: String) -> AppResult<Option<Permission>> {
        let res = PermissionBmc::get_by_id(&id).await?;
        Ok(res)
    }

    pub async fn create(req: CreateParams<PermissionCreate>) -> AppResult<String> {
        PermissionBmc::create(req.data).await?;
        let _ = Self::init_permission().await?;
        Ok("Permission created".to_string())
    }

    pub async fn update(req: UpdateParams<PermissionUpdate>) -> AppResult<String> {
        PermissionBmc::update(req.data).await?;
        let _ = Self::init_permission().await?;
        Ok("Permission updated".to_string())
    }

    pub async fn delete(id: String) -> AppResult<String> {
        PermissionBmc::delete(id).await?;
        let _ = Self::init_permission().await?;
        Ok("Permission deleted".to_string())
    }

    pub async fn init_permission() -> AppResult<String> {
        let state = "<-has_permission<-role.* AS role".to_string();
        let permissions = PermissionBmc::get_list_with_state(0, 0, None, state, vec![])
            .await
            .unwrap();

        let mut list: HashMap<String, HashMap<String, Vec<String>>> = HashMap::new();
        for permission in permissions {
            match list.get_mut(&permission.path.clone().unwrap()) {
                Some(path) => match path.get_mut(&permission.method.clone().unwrap()) {
                    Some(method) => {
                        for role in permission.role.clone().unwrap() {
                            method.push(role.id.to_string());
                        }
                    }
                    None => {
                        let mut roles = Vec::new();
                        for role in permission.role.clone().unwrap() {
                            roles.push(role.id.to_string());
                        }
                        path.insert(permission.method.clone().unwrap(), roles);
                    }
                },
                None => {
                    let mut roles = Vec::new();
                    for role in permission.role.clone().unwrap() {
                        roles.push(role.id.to_string());
                    }
                    let mut method = HashMap::new();
                    method.insert(permission.method.clone().unwrap(), roles);
                    list.insert(permission.path.clone().unwrap(), method);
                }
            }
        }
        for item in list {
            let check = redis::exists(&item.0);
            if check {
                let _ = redis::del(&item.0);
            }
            for method in item.1 {
                let _ = redis::hset(&item.0, &method.0, &method.1.join(",")); // .iter()用于遍历
            }
        }
        Ok("Permission initialized".to_string())
    }
}
