use crate::{
    app_writer::AppResult,
    db::{ListParams, WhereOptions},
    dtos::{
        attachment::AttachmentCreate,
        dict::{REPAYMENT_LOG_STATUS, REPAYMENT_LOG_TYPE},
        repayment_log::{RepaymentLogCreate, RepaymentLogUpdate},
    },
    entities::repayment_log::{RepaymentLog, RepaymentLogBmc},
    services::{attachment::AttachmentService, repayment::RepaymentService},
    utils::upload::{generate_oss_path, init_oss, UploadMethod},
};
use anyhow::anyhow;
use rust_decimal::Decimal;
use salvo::http::form::FilePart;
use std::str::FromStr;

pub struct RepaymentLogService;

impl RepaymentLogService {
    /// 获取还款记录总数
    /// # 参数
    /// * `req` - 查询条件，可选
    pub async fn get_total(req: Option<Vec<WhereOptions>>) -> AppResult<u32> {
        let mut where_options = Vec::new();
        if let Some(params) = req {
            where_options = params;
        }
        match RepaymentLogBmc::get_total(where_options).await {
            Ok(count) => {
                if let Some(count) = count {
                    Ok(count.count)
                } else {
                    Ok(0)
                }
            }
            Err(_) => Ok(0),
        }
    }

    /// 获取还款记录列表
    /// # 参数
    /// * `req` - 列表查询参数
    pub async fn get_list(req: ListParams) -> AppResult<Vec<RepaymentLog>> {
        let mut limit: u32 = 15;
        let mut offset: u32 = 0;
        if let Some(page) = req.page {
            limit = page.get_limit().unwrap_or(0);
            offset = page.get_offset();
        }
        let mut where_options = Vec::new();
        if let Some(params) = req.params {
            where_options = params;
        }
        let res = RepaymentLogBmc::get_list(offset, limit, req.options, where_options).await?;
        Ok(res)
    }

    /// 根据查询条件获取单个还款记录
    /// # 参数
    /// * `params` - 查询条件
    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<RepaymentLog> {
        match RepaymentLogBmc::get_by_query(params).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("RepaymentLog not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 根据ID获取还款记录
    /// # 参数
    /// * `id` - 还款记录ID
    pub async fn get_by_id(id: String) -> AppResult<RepaymentLog> {
        match RepaymentLogBmc::get_by_id(&id).await {
            Ok(res) => {
                if let Some(res) = res {
                    Ok(res)
                } else {
                    Err(anyhow!("RepaymentLog not found.").into())
                }
            }
            Err(e) => Err(e),
        }
    }

    /// 创建新的还款记录
    /// # 参数
    /// * `file` - 可选的附件文件
    /// * `obj` - 创建还款记录的数据
    pub async fn create(file: Option<FilePart>, obj: RepaymentLogCreate) -> AppResult<String> {
        // 1. 验证 log_type 是否有效
        let valid_log_types: Vec<&str> =
            REPAYMENT_LOG_TYPE.iter().map(|(value, _)| *value).collect();
        if !valid_log_types.contains(&obj.log_type.as_str()) {
            return Err(anyhow!(
                "无效的日志类型: {}，有效值为: {:?}",
                obj.log_type,
                valid_log_types
            )
            .into());
        }

        // 2. 如果是还款记录，验证 log_value 是否为有效的金额
        if obj.log_type == "REPAY" {
            if let Some(ref log_value) = obj.log_value {
                match Decimal::from_str(log_value) {
                    Ok(_) => {
                        // 金额格式有效
                    }
                    Err(_) => {
                        return Err(anyhow!(
                            "还款记录的 log_value 必须是有效的金额格式，当前值: {}",
                            log_value
                        )
                        .into());
                    }
                }
            } else {
                return Err(anyhow!("还款记录必须提供 log_value（金额）").into());
            }
        }

        // 3. 验证 log_status 是否有效（如果提供了）
        if let Some(ref log_status) = obj.log_status {
            let valid_log_statuses: Vec<&str> = REPAYMENT_LOG_STATUS
                .iter()
                .map(|(value, _)| *value)
                .collect();
            if !valid_log_statuses.contains(&log_status.as_str()) {
                return Err(anyhow!(
                    "无效的日志状态: {}，有效值为: {:?}",
                    log_status,
                    valid_log_statuses
                )
                .into());
            }
        }

        // 4. 创建还款日志记录
        let log_id = RepaymentLogBmc::create(obj).await?;

        // 5. 如果有文件，处理文件上传和附件创建
        if let Some(file) = file {
            // 生成上传路径
            let oss_path = generate_oss_path("repayment_log");

            // 上传文件
            let upload_method = init_oss();
            match upload_method.add_single(file, &oss_path).await {
                Ok(upload_result) => {
                    // 创建附件记录
                    let attachment_data = AttachmentCreate {
                        title: Some(upload_result.filename.clone()),
                        entity_type: "repayment_log".to_string(),
                        entity_id: Some(log_id.clone()),
                        attachment_type: Some("repayment_log_attachment".to_string()),
                        file_type: Some(upload_result.file_type),
                        save_dir: upload_result.path,
                        file_name: upload_result.filename,
                        file_link: upload_result.url,
                        thumb_name: None,
                        thumb_link: None,
                        status: "success".to_string(),
                        sort: 0, // 使用默认值
                        ..Default::default()
                    };

                    // 保存附件记录
                    match AttachmentService::create(attachment_data).await {
                        Ok(_) => {
                            // 附件创建成功
                        }
                        Err(e) => {
                            // 附件创建失败，记录错误但不影响主流程
                            eprintln!("附件创建失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    // 文件上传失败，记录错误但不影响主流程
                    eprintln!("文件上传失败: {}", e);
                }
            }
        }

        Ok(log_id)
    }

    /// 更新还款记录
    /// # 参数
    /// * `req` - 更新还款记录的数据
    pub async fn update(mut req: RepaymentLogUpdate) -> AppResult<String> {
        // 1. 验证 log_type 是否有效（如果提供了）
        if let Some(ref log_type) = req.log_type {
            let valid_log_types: Vec<&str> =
                REPAYMENT_LOG_TYPE.iter().map(|(value, _)| *value).collect();
            if !valid_log_types.contains(&log_type.as_str()) {
                return Err(anyhow!(
                    "无效的日志类型: {}，有效值为: {:?}",
                    log_type,
                    valid_log_types
                )
                .into());
            }
        }

        // 2. 如果是还款记录，验证 log_value 是否为有效的金额
        if let Some(ref log_type) = req.log_type {
            if log_type == "REPAY" {
                if let Some(ref log_value) = req.log_value {
                    match Decimal::from_str(log_value) {
                        Ok(_) => {
                            // 金额格式有效
                        }
                        Err(_) => {
                            return Err(anyhow!(
                                "还款记录的 log_value 必须是有效的金额格式，当前值: {}",
                                log_value
                            )
                            .into());
                        }
                    }
                } else {
                    return Err(anyhow!("还款记录必须提供 log_value（金额）").into());
                }
            }
        }

        // 3. 验证 log_status 是否有效（如果提供了）
        if let Some(ref log_status) = req.log_status {
            let valid_log_statuses: Vec<&str> = REPAYMENT_LOG_STATUS
                .iter()
                .map(|(value, _)| *value)
                .collect();
            if !valid_log_statuses.contains(&log_status.as_str()) {
                return Err(anyhow!(
                    "无效的日志状态: {}，有效值为: {:?}",
                    log_status,
                    valid_log_statuses
                )
                .into());
            }
        }

        // 4. 获取当前的还款日志记录以获取必要信息
        let current_log = Self::get_by_id(req.id.clone()).await?;

        // 5. 检查是否需要执行还款逻辑
        let should_process_repayment = {
            // 检查 log_type 是否为 REPAY（优先使用更新值，否则使用当前值）
            let log_type = req.log_type.as_ref().unwrap_or(&current_log.log_type);
            // 检查 log_status 是否为 approved（优先使用更新值，否则使用当前值）
            let log_status = req.log_status.as_ref().or(current_log.log_status.as_ref());

            log_type == "REPAY" && log_status == Some(&"approved".to_string())
        };

        if should_process_repayment {
            // 执行还款处理逻辑
            Self::process_repayment_approval(&current_log, &req).await?;

            // 强制设置 log_status 为 "completed"
            req.log_status = Some("completed".to_string());
        }

        // 6. 更新还款日志记录
        RepaymentLogBmc::update(req).await?;
        Ok("RepaymentLog updated".to_string())
    }

    /// 处理还款审核通过的逻辑
    /// # 参数
    /// * `current_log` - 当前的还款日志记录
    /// * `req` - 更新请求数据
    async fn process_repayment_approval(
        current_log: &RepaymentLog,
        req: &RepaymentLogUpdate,
    ) -> AppResult<()> {
        // 获取 log_value（优先使用更新值，否则使用当前值）
        let log_value_str = req
            .log_value
            .as_ref()
            .or(current_log.log_value.as_ref())
            .ok_or_else(|| anyhow!("还款记录必须提供 log_value（金额）"))?;

        // 解析还款金额
        let repayment_amount = Decimal::from_str(log_value_str)
            .map_err(|_| anyhow!("log_value 必须是有效的金额格式: {}", log_value_str))?;

        // 通过 parent_id 查询 Repayment 信息
        let mut repayment = RepaymentService::get_by_id(current_log.parent_id.clone())
            .await
            .map_err(|_| anyhow!("查询还款计划失败，parent_id: {}", current_log.parent_id))?;

        // 解析 repayment 的剩余金额字段
        let profit_remain = repayment
            .profit_remain
            .as_ref()
            .and_then(|s| Decimal::from_str(s).ok())
            .unwrap_or(Decimal::ZERO);

        let principal_remain = repayment
            .principal_remain
            .as_ref()
            .and_then(|s| Decimal::from_str(s).ok())
            .unwrap_or(Decimal::ZERO);

        let total_remain = repayment
            .total_remain
            .as_ref()
            .and_then(|s| Decimal::from_str(s).ok())
            .unwrap_or(Decimal::ZERO);

        // 计算还款逻辑
        let profit_payment = repayment_amount - profit_remain;

        let (new_profit_remain, new_principal_remain, new_total_remain) =
            if profit_payment <= Decimal::ZERO {
                // 还款金额 <= 利润剩余，只扣除利润
                let new_profit = profit_remain - repayment_amount;
                let new_total = total_remain - repayment_amount;
                (new_profit, principal_remain, new_total)
            } else {
                // 还款金额 > 利润剩余，先扣完利润，再扣本金
                let new_principal = principal_remain - profit_payment;
                let new_total = new_principal; // total_remain = 新的 principal_remain
                (Decimal::ZERO, new_principal, new_total)
            };

        // 更新 repayment 的剩余金额
        repayment.profit_remain = Some(new_profit_remain.to_string());
        repayment.principal_remain = Some(new_principal_remain.to_string());
        repayment.total_remain = Some(new_total_remain.to_string());

        // 构建更新请求
        use crate::dtos::repayment::RepaymentUpdate;
        let update_req = RepaymentUpdate {
            id: current_log.parent_id.clone(),
            profit_remain: Some(new_profit_remain.to_string()),
            principal_remain: Some(new_principal_remain.to_string()),
            total_remain: Some(new_total_remain.to_string()),
            ..Default::default()
        };

        // 保存 repayment
        RepaymentService::update(update_req)
            .await
            .map_err(|e| anyhow!("更新还款计划失败: {}", e))?;

        Ok(())
    }

    /// 删除还款记录
    /// # 参数
    /// * `id` - 要删除的还款记录ID
    pub async fn delete(id: String) -> AppResult<String> {
        RepaymentLogBmc::delete(id).await?;
        Ok("RepaymentLog deleted".to_string())
    }
}
