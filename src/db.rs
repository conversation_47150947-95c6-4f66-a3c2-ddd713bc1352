use crate::app_writer::AppResult;
use crate::config::CFG;
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::de::{self, Deserializer, Visitor};
use serde::{de::DeserializeOwned, Deserialize, Serialize};
use std::fmt;
use std::str::FromStr;
use std::sync::LazyLock;
use surrealdb::engine::remote::ws::{Client, Ws};
use surrealdb::opt::auth::Root;
use surrealdb::{sql::Thing, Surreal};

const PAGE_LIMIT: u32 = 15;

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct Page {
    pub limit: u32,
    pub page: u32,
}

impl Page {
    pub fn default() -> Self {
        Page {
            limit: PAGE_LIMIT,
            page: 0,
        }
    }

    pub fn unlimited() -> Self {
        Page { limit: 0, page: 0 }
    }

    pub fn get_limit(&self) -> anyhow::Result<u32> {
        if self.limit < 1 {
            return Err(anyhow::anyhow!("page limit must be greater than 0"));
        }
        Ok(self.limit)
    }

    pub fn get_offset(&self) -> u32 {
        if self.page == 0 {
            return 0;
        }
        (self.page - 1) * self.limit
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct FlexibleParams {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub options: Option<ListOptions>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<IdParams>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub params: Option<Vec<WhereOptions>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub page: Option<Page>,
}

// 为FlexibleParams实现默认值
impl Default for FlexibleParams {
    fn default() -> Self {
        Self {
            options: None,
            id: None,
            params: None,
            page: None,
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct ListOptions {
    pub order_by: Option<String>,
    pub desc: Option<bool>,
}

impl ListOptions {
    pub fn default() -> Self {
        ListOptions {
            order_by: Some("created_at".to_string()),
            desc: Some(true),
        }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct WhereOptions {
    pub var: String,
    pub val: String,
}

impl WhereOptions {
    pub fn get_sql(&self) -> String {
        match self.var.as_str() {
            "id" => format!("{} = {}", self.var, self.val),
            "ids" => format!("id IN [{}]", self.val),
            "owner" => format!("owner = {}", self.val),
            "sender" => format!("sender = {}", self.val),
            "receiver" => format!("receiver = {}", self.val),
            "user" => format!("user = {}", self.val),
            "users" => format!("user IN [{}]", self.val),
            "role" => format!("role = {}", self.val),
            "group" => format!("group = {}", self.val),
            "roles" => format!("role IN [{}]", self.val),
            "groups" => format!("group IN [{}]", self.val),
            "usage" => format!("usage = {}", self.val),
            "usages" => format!("usage IN [{}]", self.val),
            "tag" => format!("tag = {}", self.val),
            "tags" => format!("tag IN [{}]", self.val),
            "activity" => format!("activity = {}", self.val),
            "activities" => format!("activity IN [{}]", self.val),
            "begin_date" => format!("date >= '{}'", self.val),
            "end_date" => format!("date <= '{}'", self.val),
            "ralate" | "raw" => self.val.clone(),
            _ if self.var.contains(".id") || self.var.contains("_id") => {
                format!("{} = {}", self.var, self.val)
            }
            _ => format!("{} = '{}'", self.var, self.val),
        }
    }

    pub fn new(var: String, val: String) -> Self {
        WhereOptions { var, val }
    }
}

/// 字段更新选项，用于多字段更新
#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
pub struct UpdateOptions {
    pub field: String,
    pub value: String,
}

impl UpdateOptions {
    /// 生成SQL SET子句
    pub fn get_sql(&self) -> String {
        match self.field.as_str() {
            // ID字段不需要引号
            _ if self.field.contains(".id") || self.field.contains("_id") => {
                format!("{} = {}", self.field, self.value)
            }
            // 数值字段不需要引号
            "amount" | "express_fee" | "total_payment" | "platform_fee_total" | "sales_price"
            | "cost_price" | "platform_fee" | "discount" | "quantity" | "total_sales_price"
            | "total_cost_price" | "created_at" | "updated_at" => {
                format!("{} = {}", self.field, self.value)
            }
            // 其他字段需要引号
            _ => format!("{} = '{}'", self.field, self.value),
        }
    }

    pub fn new(field: String, value: String) -> Self {
        UpdateOptions { field, value }
    }
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct IdParams {
    pub id: String,
    pub status: Option<String>,
    pub confirm: Option<bool>,
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct IdsParams {
    pub ids: Vec<String>,
    pub update_fields: Vec<UpdateOptions>,
}

#[derive(Deserialize, Debug)]
pub struct CreateParams<D> {
    pub data: D,
}

#[derive(Deserialize, Debug)]
pub struct UpdateParams<D> {
    pub data: D,
}

#[derive(Deserialize, Debug, Clone, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct RelateParams {
    pub from: String,
    pub to: Vec<String>,
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct ListParams {
    pub page: Option<Page>,
    pub options: Option<ListOptions>,
    pub params: Option<Vec<WhereOptions>>,
}

#[derive(Debug, Deserialize, Serialize, Extractible, ToSchema, Clone)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct MessageListParams {
    pub page: Option<Page>,
    pub options: Option<ListOptions>,
    pub req_type: String,
}

#[derive(Default, Serialize, Debug, ToSchema)]
pub struct ListResponse<T: Serialize> {
    pub data: Vec<T>,
    pub total: u32,
    pub page: u32,
    pub size: u32,
}

static GDB: LazyLock<Surreal<Client>> = LazyLock::new(Surreal::init);

pub async fn init_db() -> Result<(), surrealdb::Error> {
    let host = format!("{}:{}", &CFG.database.url, &CFG.database.port);
    let username = &CFG.database.username;
    let password = &CFG.database.password;
    let ns = &CFG.database.db_ns;
    let db_name = &CFG.database.db_name;

    println!("数据库连接信息：{}", host);

    GDB.connect::<Ws>(host).await?;

    GDB.signin(Root {
        username: &username,
        password: &password,
    })
    .await?;

    GDB.use_ns(ns).use_db(db_name).await.unwrap();

    Ok(())
}

pub trait Castable: DeserializeOwned {}
pub trait Creatable: Serialize {}
pub trait Patchable: Serialize {}

#[derive(Debug, Deserialize, Serialize)]
pub struct Record {
    pub id: Thing,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CountRecord {
    pub count: u32,
}

#[derive(Debug, Default, Deserialize, Serialize, Clone)]
pub struct SumForChart {
    #[serde(deserialize_with = "flexible_string_deserializer")]
    pub field: String,
    #[serde(deserialize_with = "flexible_string_deserializer")]
    pub label: String,
    pub value: f64,
}

fn flexible_string_deserializer<'de, D>(deserializer: D) -> Result<String, D::Error>
where
    D: Deserializer<'de>,
{
    struct FlexibleStringVisitor;

    impl<'de> Visitor<'de> for FlexibleStringVisitor {
        type Value = String;

        fn expecting(&self, formatter: &mut fmt::Formatter) -> fmt::Result {
            formatter.write_str("string, integer, Thing, or map")
        }

        fn visit_str<E: de::Error>(self, v: &str) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_i64<E: de::Error>(self, v: i64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_u64<E: de::Error>(self, v: u64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }

        fn visit_f64<E: de::Error>(self, v: f64) -> Result<Self::Value, E> {
            Ok(v.to_string())
        }
    }

    deserializer.deserialize_any(FlexibleStringVisitor)
}
pub struct Database;

#[allow(dead_code)]
impl Database {
    /// 清空数据库中的所有数据
    ///
    /// # 参数
    /// * `table` - 要清空的表名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_clean_db(table: &str) -> AppResult<String> {
        let sql = format!("DELETE {} RETURN NONE;", table);
        GDB.query(sql).await?;
        Ok(format!("表 {} 已清空", table))
    }
    /// 根据查询条件统计记录数量
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `params` - 查询条件列表
    ///
    /// # 返回值
    /// * `AppResult<Option<CountRecord>>` - 操作结果，包含统计数量或None
    ///
    /// # 功能
    /// - 根据提供的查询条件统计表中匹配的记录数量
    /// - 使用GROUP ALL进行聚合统计
    /// - 支持多个AND条件组合查询
    pub async fn exec_query_count(
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<CountRecord>> {
        let mut sql = format!("SELECT COUNT() FROM {}", table);

        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        sql.push_str(" GROUP ALL;");
        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }

    /// 根据查询条件分页查询记录列表
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `page` - 页码起始位置（用于LIMIT START）
    /// * `limit` - 每页记录数量限制，0表示不限制
    /// * `options` - 排序选项，包含排序字段和排序方向
    /// * `params` - 查询条件列表
    ///
    /// # 返回值
    /// * `AppResult<Vec<T>>` - 操作结果，返回符合条件的记录列表
    ///
    /// # 功能
    /// - 支持分页查询，通过page和limit控制返回结果
    /// - 支持多条件AND组合查询
    /// - 支持自定义排序字段和排序方向
    /// - 泛型T必须实现Castable trait
    pub async fn exec_query_list<T: Castable>(
        table: &str,
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<T>> {
        let mut sql = format!("SELECT * FROM {} ", table);

        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        if let Some(options) = options {
            sql.push_str(" ORDER BY ");

            if let Some(order_by) = options.order_by {
                sql.push_str(&order_by);
            }

            match options.desc {
                Some(true) => sql.push_str(" DESC"),
                Some(false) => sql.push_str(" ASC"),
                None => sql.push_str(" ASC"),
            }
        }

        if limit > 0 {
            sql.push_str(&format!(" LIMIT {} START {}", limit, page));
        };

        sql.push_str(" ;");
        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }

    /// 根据ID查询单条记录
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `tid` - 记录ID，支持完整格式（table:id）或简单格式（id）
    ///
    /// # 返回值
    /// * `AppResult<Option<T>>` - 操作结果，返回匹配的记录或None
    ///
    /// # 功能
    /// - 支持两种ID格式：完整格式（table:id）和简单格式（id）
    /// - 自动处理ID格式转换
    /// - 泛型T必须实现Castable trait
    pub async fn exec_get_by_id<T: Castable>(table: &str, tid: &str) -> AppResult<Option<T>> {
        let parts: Vec<&str> = tid.split(':').collect();
        if parts.len() == 2 {
            let res = GDB.select((table, parts[1])).await?;
            return Ok(res);
        }
        let res = GDB.select((table, tid)).await?;
        Ok(res)
    }

    /// 根据查询条件获取单条记录
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `params` - 查询条件列表
    ///
    /// # 返回值
    /// * `AppResult<Option<T>>` - 操作结果，返回匹配的第一条记录或None
    ///
    /// # 功能
    /// - 根据多个查询条件查找匹配的记录
    /// - 使用LIMIT 1限制只返回第一条匹配记录
    /// - 支持多个AND条件组合查询
    /// - 泛型T必须实现Castable trait
    pub async fn exec_get_by_query<T: Castable>(
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<T>> {
        let mut sql = format!("SELECT * FROM {} WHERE ", table);
        for (i, param) in params.iter().enumerate() {
            if i > 0 {
                sql.push_str(" AND ");
            }
            sql.push_str(param.get_sql().as_str());
        }

        sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);
        let mut ress = GDB.query(sql).await?;
        let res = ress.take(0)?;
        Ok(res)
    }

    /// 根据查询条件获取指定字段值
    ///
    /// # 参数
    /// * `field` - 要查询的字段名
    /// * `table` - 要查询的表名
    /// * `params` - 查询条件列表
    ///
    /// # 返回值
    /// * `AppResult<Option<T>>` - 操作结果，返回指定字段的值或None
    ///
    /// # 功能
    /// - 只查询指定字段而不是整条记录，提高查询效率
    /// - 使用LIMIT 1限制只返回第一条匹配记录的字段值
    /// - 支持多个AND条件组合查询
    /// - 泛型T必须实现Castable trait
    pub async fn exec_get_field_by_query<T: Castable>(
        field: &str,
        table: &str,
        params: Vec<WhereOptions>,
    ) -> AppResult<Option<T>> {
        let mut sql = format!("SELECT {} FROM {} WHERE ", field, table);
        for (i, param) in params.iter().enumerate() {
            if i > 0 {
                sql.push_str(" AND ");
            }
            sql.push_str(param.get_sql().as_str());
        }

        sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);
        let mut ress = GDB.query(sql).await?;
        let res = ress.take(0)?;
        Ok(res)
    }

    /// 创建新记录
    ///
    /// # 参数
    /// * `table` - 要创建记录的表名
    /// * `obj` - 要创建的记录对象，必须实现Creatable trait
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回新创建记录的ID
    ///
    /// # 功能
    /// - 在指定表中创建新记录
    /// - 自动生成记录ID
    /// - 泛型T必须实现Creatable trait和'static生命周期
    pub async fn exec_create<T: Creatable + 'static>(table: &str, obj: T) -> AppResult<String> {
        let ress: Option<Record> = GDB.create(table).content(obj).await?;
        match ress {
            Some(res) => Ok(res.id.to_raw()),
            None => Err(anyhow::anyhow!("create failed").into()),
        }
    }

    /// 根据ID更新记录
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `tid` - 记录ID，支持完整格式（table:id）或简单格式（id）
    /// * `obj` - 要更新的数据对象，必须实现Patchable trait
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新的记录ID
    ///
    /// # 功能
    /// - 根据ID更新指定记录
    /// - 自动处理ID格式转换（去除table前缀）
    /// - 使用merge方式进行部分字段更新
    /// - 泛型T必须实现Patchable trait和'static生命周期
    pub async fn exec_update<T: Patchable + 'static>(
        table: &str,
        tid: &str,
        obj: T,
    ) -> AppResult<String> {
        let final_tid = if tid.to_string().contains(':') {
            tid.to_string()
                .split(':')
                .nth(1)
                .unwrap_or(&tid)
                .to_string()
        } else {
            tid.to_string().clone()
        };
        let res: Option<Record> = GDB.update((table, &final_tid)).merge(obj).await?;
        match res {
            Some(_) => Ok(tid.to_string()),
            None => Err(anyhow::anyhow!("update failed, table: {}, tid: {}", table, tid).into()),
        }
    }

    /// 根据ID删除记录
    ///
    /// # 参数
    /// * `table` - 要删除记录的表名
    /// * `tid` - 记录ID，支持完整格式（table:id）或简单格式（id）
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回被删除的记录ID
    ///
    /// # 功能
    /// - 根据ID删除指定记录
    /// - 自动处理ID格式转换（去除table前缀）
    /// - 返回原始ID格式用于确认删除操作
    pub async fn exec_delete(table: &str, tid: String) -> AppResult<String> {
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid.clone()
        };

        let res: Option<Record> = GDB.delete((table, &final_tid)).await?;
        match res {
            Some(_) => Ok(tid),
            None => Err(anyhow::anyhow!("delete failed, table: {}, tid: {}", table, tid).into()),
        }
    }

    /// 创建两个记录之间的关联关系
    ///
    /// # 参数
    /// * `table` - 关联表名（关系类型）
    /// * `from` - 源记录ID
    /// * `to` - 目标记录ID
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回关联关系描述
    ///
    /// # 功能
    /// - 在两个记录之间创建关联关系
    /// - 使用SurrealDB的RELATE语法：from -> table -> to
    /// - 对应数据库中的 in -> table -> out 结构
    pub async fn exec_relate(table: &str, from: &str, to: &str) -> AppResult<String> {
        let sql = format!("RELATE {}->{}->{};", from, table, to);
        println!("SQL语句：{}", sql);
        let res: Option<Record> = GDB.query(sql).await?.take(0)?;
        match res {
            Some(_) => Ok(format!("{from} -> {table} -> {to}")),
            None => Err(anyhow::anyhow!(
                "relate failed, table: {}, from: {}, to: {}",
                table,
                from,
                to
            )
            .into()),
        }
    }

    /// 批量创建记录之间的关联关系
    ///
    /// # 参数
    /// * `table` - 关联表名（关系类型）
    /// * `from` - 源记录ID列表
    /// * `to` - 目标记录ID列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回成功消息
    ///
    /// # 功能
    /// - 批量创建多个记录之间的关联关系
    /// - 支持一对一、一对多、多对多的关联
    /// - 自动处理单个ID和ID数组的格式转换
    /// - 对应数据库中的 in -> table -> out 结构
    pub async fn exec_relate_batch(
        table: &str,
        from: Vec<String>,
        to: Vec<String>,
    ) -> AppResult<String> {
        let from_id = if from.len() == 1 {
            from[0].clone()
        } else {
            format!("[{}]", from.join(","))
        };

        let to_id = if to.len() == 1 {
            to[0].clone()
        } else {
            format!("[{}]", to.join(","))
        };
        let sql = format!("RELATE {}->{}->{};", from_id, table, to_id);
        println!("SQL语句：{}", sql);
        GDB.query(sql).await?;
        Ok("Items has been related !".to_string())
    }

    /// 查询关联关系记录列表
    ///
    /// # 参数
    /// * `table` - 关联表名
    /// * `page` - 页码起始位置（用于LIMIT START）
    /// * `limit` - 每页记录数量限制，0表示不限制
    /// * `state` - 要查询的关联状态字段
    /// * `options` - 排序选项，包含排序字段和排序方向
    /// * `params` - 查询条件列表
    ///
    /// # 返回值
    /// * `AppResult<Vec<T>>` - 操作结果，返回符合条件的关联记录列表
    ///
    /// # 功能
    /// - 查询关联表中的记录，同时包含关联状态信息
    /// - 支持分页查询和多条件筛选
    /// - 支持自定义排序，默认按create_at排序
    /// - 泛型T必须实现Castable trait
    pub async fn exec_query_relate<T: Castable>(
        table: &str,
        page: u32,
        limit: u32,
        state: &str,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<T>> {
        let mut sql = format!("SELECT *, {} FROM {} ", state, table);
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        if let Some(options) = options {
            sql.push_str(" ORDER BY ");

            if let Some(order_by) = options.order_by {
                sql.push_str(&order_by);
            } else {
                sql.push_str("create_at");
            }

            if let Some(_desc) = options.desc {
                sql.push_str(" DESC");
            } else {
                sql.push_str(" ASC");
            }
        }

        if limit > 0 {
            sql.push_str(&format!(" LIMIT {} START {}", limit, page));
        };

        sql.push_str(" ;");
        println!("SQL语句：{}", sql);
        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }

    /// 返回指定关联关系的ID列表
    ///
    /// # 参数
    /// * `table` - 关联表名
    /// * `from` - 源记录ID
    /// * `to` - 目标记录ID
    ///
    /// # 返回值
    /// * `AppResult<Vec<String>>` - 操作结果，返回关联关系ID列表
    ///
    /// # 功能
    /// - 查询指定源和目标之间的关联关系ID
    /// - 使用RETURN语句获取关联记录的ID
    /// - 将Thing类型的ID转换为字符串格式
    pub async fn exec_return_relate(table: &str, from: &str, to: &str) -> AppResult<Vec<String>> {
        let sql = format!("RETURN {}->{}->{}", from, table, to);
        println!("SQL语句：{}", sql);
        let mut response = GDB.query(sql).await?;
        let list: Vec<Thing> = response.take(0)?;
        let mut res = Vec::new();
        for item in list {
            res.push(item.to_raw());
        }
        Ok(res)
    }

    /// 删除指定的关联关系
    ///
    /// # 参数
    /// * `table` - 关联表名
    /// * `from` - 源记录ID
    /// * `to` - 目标记录ID
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回被删除的关联关系描述
    ///
    /// # 功能
    /// - 删除指定源和目标之间的关联关系
    /// - 使用WHERE条件精确匹配要删除的关联
    /// - 返回删除前的记录信息用于确认
    pub async fn exec_unrelate(table: &str, from: &str, to: &str) -> AppResult<String> {
        let sql = format!("DELETE {from}->{table} WHERE out={to} RETURN BEFORE;");
        println!("SQL语句：{}", sql);
        let res: Option<Record> = GDB.query(sql).await?.take(0)?;
        match res {
            Some(_) => Ok(format!("{from} -> {table} -> {to}")),
            None => Err(anyhow::anyhow!(
                "delete relate failed, table: {}, from: {}, to: {}",
                table,
                from,
                to
            )
            .into()),
        }
    }

    /// 根据指定ID更新单条记录
    ///
    /// # 参数
    /// * `t_id` - 要更新的表名
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_update_by_id(
        t_id: &str,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        let sql = format!("UPDATE {} SET {} = '{}'", t_id, update_field, update_value);

        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let res: Option<Record> = response.take(0)?;

        match res {
            Some(record) => Ok(record.id.to_raw()),
            None => {
                Err(
                    anyhow::anyhow!("update by id failed, id: {}, field: {}", t_id, update_field)
                        .into(),
                )
            }
        }
    }

    /// 根据查询条件更新单条记录
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_update_by_query(
        table: &str,
        params: Vec<WhereOptions>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        let mut sql = format!("UPDATE {} SET {} = {}", table, update_field, update_value);

        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        // sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let res: Option<Record> = response.take(0)?;

        match res {
            Some(record) => Ok(record.id.to_raw()),
            None => Err(anyhow::anyhow!(
                "update by query failed, table: {}, field: {}",
                table,
                update_field
            )
            .into()),
        }
    }

    /// 根据查询条件更新多个字段
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `params` - 查询条件
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果
    pub async fn exec_update_multiple_fields_by_query(
        table: &str,
        params: Vec<WhereOptions>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        if update_fields.is_empty() {
            return Err(anyhow::anyhow!("update fields cannot be empty").into());
        }

        let mut sql = format!("UPDATE {} SET ", table);

        // 构建SET子句
        for (i, field) in update_fields.iter().enumerate() {
            if i > 0 {
                sql.push_str(", ");
            }
            sql.push_str(&field.get_sql());
        }

        // 构建WHERE子句
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        // sql.push_str(" LIMIT 1;");
        println!("SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let res: Option<Record> = response.take(0)?;

        match res {
            Some(record) => Ok(record.id.to_raw()),
            None => Err(anyhow::anyhow!(
                "update multiple fields by query failed, table: {}",
                table
            )
            .into()),
        }
    }

    /// 根据查询条件对指定字段进行求和统计
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `params` - 查询条件
    /// * `count_field` - 要进行求和的字段名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回求和结果或错误信息
    ///
    /// # 功能
    /// - 通过SQL测试字段是否为可汇总类型（数值类型）
    /// - 如果字段不可汇总，返回错误
    /// - 如果可以汇总，执行SQL求和并返回结果
    pub async fn exec_sum(
        table: &str,
        params: Vec<WhereOptions>,
        count_field: &str,
    ) -> AppResult<String> {
        // 首先通过SQL测试字段是否可以进行汇总
        // 构建测试SQL：尝试对字段进行求和操作，限制返回1条记录以提高性能
        let test_sql = format!(
            "SELECT math::sum({}) AS test_sum FROM {} LIMIT 1;",
            count_field, table
        );
        println!("字段类型测试SQL：{}", test_sql);

        // 执行测试查询
        let mut test_response = GDB.query(test_sql).await;

        match test_response {
            Ok(ref mut response) => {
                // 尝试获取测试结果
                let test_result: Result<Option<serde_json::Value>, _> = response.take(0);

                match test_result {
                    Ok(_) => {
                        // 测试成功，说明字段可以进行汇总
                        println!("字段 '{}' 类型验证通过，可以进行汇总", count_field);
                    }
                    Err(e) => {
                        // 测试失败，说明字段不能进行汇总
                        return Err(anyhow::anyhow!(
                            "字段 '{}' 不是可汇总的数值类型字段。SurrealDB错误: {}。请确保字段存在且为数值类型（如：整数、浮点数、Decimal等）。",
                            count_field,
                            e
                        ).into());
                    }
                }
            }
            Err(e) => {
                // 查询执行失败
                return Err(anyhow::anyhow!(
                    "无法验证字段 '{}' 的类型。可能原因：1) 字段不存在，2) 表不存在，3) 字段不是数值类型。SurrealDB错误: {}",
                    count_field,
                    e
                ).into());
            }
        }

        // 字段验证通过，构建实际的求和SQL查询语句
        let mut sql = format!(
            "SELECT math::sum({}) AS sum_result FROM {}",
            count_field, table
        );

        // 添加WHERE条件
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        sql.push_str(";");
        println!("实际求和SQL语句：{}", sql);

        // 执行实际的求和查询
        let mut response = GDB.query(sql).await?;

        // 尝试获取结果
        let result: Option<serde_json::Value> = response.take(0)?;

        match result {
            Some(value) => {
                // 从结果中提取sum_result字段
                if let Some(sum_value) = value.get("sum_result") {
                    match sum_value {
                        serde_json::Value::Number(num) => {
                            Ok(format!("字段 '{}' 的汇总结果: {}", count_field, num))
                        }
                        serde_json::Value::Null => Ok(format!(
                            "字段 '{}' 的汇总结果: 0 (无匹配数据或字段值为空)",
                            count_field
                        )),
                        _ => Err(anyhow::anyhow!(
                            "字段 '{}' 返回了非数值类型的结果，无法进行汇总",
                            count_field
                        )
                        .into()),
                    }
                } else {
                    Err(anyhow::anyhow!(
                        "查询结果中未找到汇总字段，可能字段 '{}' 不存在",
                        count_field
                    )
                    .into())
                }
            }
            None => Ok(format!("字段 '{}' 的汇总结果: 0 (无匹配数据)", count_field)),
        }
    }

    /// 根据查询条件对指定字段进行求和统计（快速版本，跳过预验证）
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `params` - 查询条件
    /// * `count_field` - 要进行求和的字段名
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回求和结果或错误信息
    ///
    /// # 功能
    /// - 直接执行求和查询，不进行预先的字段类型验证
    /// - 性能更高，但错误信息可能不如标准版本详细
    /// - 适用于已知字段类型正确的场景
    pub async fn exec_sum_fast(
        table: &str,
        params: Vec<WhereOptions>,
        count_field: &str,
    ) -> AppResult<String> {
        // 直接构建求和SQL查询语句，不进行预验证
        let mut sql = format!(
            "SELECT math::sum({}) AS sum_result FROM {}",
            count_field, table
        );

        // 添加WHERE条件
        if params.len() > 0 {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        sql.push_str(";");
        println!("快速求和SQL语句：{}", sql);

        // 执行求和查询
        let mut response = GDB.query(sql).await;

        match response {
            Ok(ref mut resp) => {
                // 尝试获取结果
                let result: Result<Option<serde_json::Value>, _> = resp.take(0);

                match result {
                    Ok(Some(value)) => {
                        // 从结果中提取sum_result字段
                        if let Some(sum_value) = value.get("sum_result") {
                            match sum_value {
                                serde_json::Value::Number(num) => {
                                    Ok(format!("字段 '{}' 的汇总结果: {}", count_field, num))
                                }
                                serde_json::Value::Null => {
                                    Ok(format!("字段 '{}' 的汇总结果: 0 (无匹配数据或字段值为空)", count_field))
                                }
                                _ => {
                                    Err(anyhow::anyhow!(
                                        "字段 '{}' 返回了非数值类型的结果，无法进行汇总",
                                        count_field
                                    ).into())
                                }
                            }
                        } else {
                            Err(anyhow::anyhow!(
                                "查询结果中未找到汇总字段，可能字段 '{}' 不存在",
                                count_field
                            ).into())
                        }
                    }
                    Ok(None) => {
                        Ok(format!("字段 '{}' 的汇总结果: 0 (无匹配数据)", count_field))
                    }
                    Err(e) => {
                        Err(anyhow::anyhow!(
                            "字段 '{}' 求和失败。可能原因：1) 字段不存在，2) 字段不是数值类型，3) SQL语法错误。SurrealDB错误: {}",
                            count_field,
                            e
                        ).into())
                    }
                }
            }
            Err(e) => Err(anyhow::anyhow!(
                "执行求和查询失败。表: '{}', 字段: '{}'。SurrealDB错误: {}",
                table,
                count_field,
                e
            )
            .into()),
        }
    }

    /// 使用SurrealDB内置函数对字符串格式的数值字段进行求和
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `params` - 查询条件
    /// * `count_field` - 要进行求和的字段名（存储为字符串格式的数值）
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回求和结果或错误信息
    ///
    /// # 功能
    /// - 在数据库侧将字符串字段转换为数值并求和
    /// - 使用SurrealDB的类型转换<decimal>和math::sum()函数
    /// - 性能更高，减少数据传输量
    pub async fn exec_sum_string_field_db(
        table: &str,
        params: Vec<WhereOptions>,
        count_field: &str,
    ) -> AppResult<String> {
        // 构建SQL查询语句，只选择需要的字段
        let mut sql = format!("SELECT {} FROM {}", count_field, table);

        // 添加WHERE条件
        if !params.is_empty() {
            sql.push_str(" WHERE ");
            for (i, param) in params.iter().enumerate() {
                if i > 0 {
                    sql.push_str(" AND ");
                }
                sql.push_str(param.get_sql().as_str());
            }
        }

        sql.push_str(";");
        println!("字符串字段求和SQL语句：{}", sql);

        // 执行查询
        let mut response = GDB.query(sql).await?;

        // 获取结果集
        let results: Vec<serde_json::Value> = response.take(0)?;

        if results.is_empty() {
            return Ok(format!("字段 '{}' 的汇总结果: 0 (无匹配数据)", count_field));
        }

        // 初始化总和为0
        let mut sum = Decimal::from(0);
        let mut count = 0;

        // 遍历结果集，提取字段值并转换为Decimal进行求和
        for item in results {
            if let Some(field_value) = item.get(count_field) {
                if let Some(str_value) = field_value.as_str() {
                    match Decimal::from_str(str_value) {
                        Ok(decimal_value) => {
                            sum += decimal_value;
                            count += 1;
                        }
                        Err(_) => {
                            println!("警告: 无法将值 '{}' 转换为数值，已跳过", str_value);
                        }
                    }
                }
            }
        }

        if count > 0 {
            Ok(sum.to_string())
        } else {
            Ok(format!(
                "字段 '{}' 的汇总结果: 0 (所有值均无法转换为数值)",
                count_field
            ))
        }
    }

    /// 批量更新多条记录的单个字段
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `ids` - 要更新的记录ID列表
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新的记录数量
    ///
    /// # 示例
    /// ```rust
    /// let ids = vec!["record1".to_string(), "record2".to_string()];
    /// let result = Database::exec_batch_update_field(
    ///     "sales_order",
    ///     ids,
    ///     "status",
    ///     "completed"
    /// ).await?;
    /// ```
    pub async fn exec_batch_update_field(
        table: &str,
        ids: Vec<String>,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        if ids.is_empty() {
            return Err(anyhow::anyhow!("ID list cannot be empty").into());
        }

        // 构建ID列表，确保格式正确
        let id_list: Vec<String> = ids
            .iter()
            .map(|id| {
                if id.contains(':') {
                    id.clone()
                } else {
                    format!("{}:{}", table, id)
                }
            })
            .collect();

        // 使用SurrealQL的UPDATE语句批量更新
        let sql = format!(
            "UPDATE [{}] SET {} = '{}' RETURN id;",
            id_list.join(", "),
            update_field,
            update_value
        );

        println!("批量更新字段SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let updated_records: Vec<Record> = response.take(0)?;

        Ok(format!(
            "成功更新 {} 条记录的字段 '{}'",
            updated_records.len(),
            update_field
        ))
    }

    /// 批量更新多条记录的多个字段
    ///
    /// # 参数
    /// * `table` - 要更新的表名
    /// * `ids` - 要更新的记录ID列表
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新的记录数量
    ///
    /// # 示例
    /// ```rust
    /// let ids = vec!["record1".to_string(), "record2".to_string()];
    /// let fields = vec![
    ///     UpdateOptions::new("status".to_string(), "completed".to_string()),
    ///     UpdateOptions::new("updated_at".to_string(), "2024-01-01T00:00:00Z".to_string()),
    /// ];
    /// let result = Database::exec_batch_update_multiple_fields(
    ///     "sales_order",
    ///     ids,
    ///     fields
    /// ).await?;
    /// ```
    pub async fn exec_batch_update_multiple_fields(
        table: &str,
        ids: Vec<String>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        if ids.is_empty() {
            return Err(anyhow::anyhow!("ID list cannot be empty").into());
        }

        if update_fields.is_empty() {
            return Err(anyhow::anyhow!("Update fields cannot be empty").into());
        }

        // 构建ID列表，确保格式正确
        let id_list: Vec<String> = ids
            .iter()
            .map(|id| {
                if id.contains(':') {
                    id.clone()
                } else {
                    format!("{}:{}", table, id)
                }
            })
            .collect();

        // 构建SET子句
        let set_clause: Vec<String> = update_fields.iter().map(|field| field.get_sql()).collect();

        // 使用SurrealQL的UPDATE语句批量更新多个字段
        let sql = format!(
            "UPDATE {} SET {} RETURN id;",
            id_list.join(", "),
            set_clause.join(", ")
        );

        println!("批量更新多字段SQL语句：{}", sql);

        let mut response = GDB.query(sql).await?;
        let updated_records: Vec<Record> = response.take(0)?;

        Ok(format!(
            "成功更新 {} 条记录的 {} 个字段",
            updated_records.len(),
            update_fields.len()
        ))
    }

    /// 按月份汇总数据用于图表展示
    ///
    /// # 参数
    /// * `table` - 要查询的表名
    /// * `state` - 状态字段名，用作图表的field
    /// * `label` - 标签字段名，用作图表的label
    /// * `year` - 指定年份，必须提供年份进行查询
    /// * `params` - 额外的查询条件列表
    ///
    /// # 返回值
    /// * `AppResult<Vec<SumForChart>>` - 操作结果，返回图表数据列表
    ///
    /// # 功能
    /// - 按指定年份和月份对amount字段进行求和统计
    /// - 使用time::year函数过滤指定年份的数据
    /// - 按field和label进行分组统计
    /// - 结果按field和label升序排列
    /// - 专门用于生成图表数据
    ///
    /// # 限制
    /// - 必须按年份查询，不支持跨年度统计
    /// - 固定对amount字段进行求和
    pub async fn exec_summary_by_month(
        table: &str,
        state: &str,
        label: &str,
        year: i32,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<SumForChart>> {
        let mut sql = format!(
            "
            SELECT
                {} AS field,
                {} AS label,
                math::sum(amount) AS value
            FROM {}
            WHERE time::year(<datetime>date) = {}",
            state, label, table, year
        );
        if params.len() > 0 {
            sql.push_str(" AND ");
            for param in params.iter() {
                sql.push_str(param.get_sql().as_str());
            }
        }
        sql.push_str(
            "
            GROUP BY
                field, label
            ORDER BY
                field, label ASC;",
        );
        println!("SQL语句：{}", sql);
        let mut response = GDB.query(sql).await?;
        let res = response.take(0)?;
        Ok(res)
    }
}
