use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::default_now;
use crate::utils::decimal_serde::deserialize_decimal_from_float_or_string;
use rust_decimal::Decimal;
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Deserialize, Serialize, Debug, Validate, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct FinancialContractCreate {
    pub name: String,                  // 项目名称
    pub serial: Option<String>,        // 合同编号
    pub begin_time: Option<String>,    // 生效日期
    pub end_time: Option<String>,      // 失效时间
    pub applier: Option<String>,       // 申请人
    pub funder: Option<String>,        // 资金方
    pub scf_company: Option<String>,   // 运营商
    pub contract_type: Option<String>, // 合同类型
    pub status: String,                // 状态
    #[serde(deserialize_with = "deserialize_decimal_from_float_or_string")]
    pub application_quota: Decimal,
    #[serde(deserialize_with = "deserialize_decimal_from_float_or_string")]
    pub confirm_quota: Decimal,
    #[serde(deserialize_with = "deserialize_decimal_from_float_or_string")]
    pub used_quota: Decimal,
    pub is_delete: Option<bool>,
    pub desc: Option<String>, // 描述``
    pub created_by: Option<String>,
    pub sign_time: Option<String>,           // 签订时间
    pub supplier_id: Option<String>,         // 供应商
    pub supplier_name: Option<String>,       // 供应商名称
    pub bid_winner_id: Option<String>,       // 中标方ID
    pub bid_winner_name: Option<String>,     // 中标方名称
    pub bid_owner_id: Option<String>,        // 招标方/终端
    pub bid_owner_name: Option<String>,      // 招标方/终端
    pub warehouse_id: Option<String>,        // 物流/仓储方
    pub warehouse_name: Option<String>,      // 物流/仓储方
    #[serde(deserialize_with = "deserialize_decimal_from_float_or_string")]
    pub profit_calc_fee: Decimal,            // 利润计算费用
    #[serde(deserialize_with = "deserialize_decimal_from_float_or_string")]
    pub penalty_calc_fee: Decimal,           // 违约金计算费用
    pub profit_calc_period: Option<String>,  // 计算周期
    pub penalty_calc_period: Option<String>, // 违约金计算方式
    pub product_category: Vec<String>,       // 商品目录，多个逗号分隔
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for FinancialContractCreate {}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
#[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct FinancialContractUpdate {
    pub id: String,
    pub name: String,                       // 项目名称
    pub serial: Option<String>,             // 合同编号
    pub begin_time: Option<String>,         // 生效日期
    pub end_time: Option<String>,           // 失效时间
    pub applier: Option<String>,            // 申请人
    pub funder: Option<String>,             // 资金方
    pub scf_company: Option<String>,        // 运营商
    pub contract_type: Option<String>,      // 合同类型
    pub status: Option<String>,             // 状态
    pub application_quota: Option<Decimal>, // 合同金额(授信额度)
    pub confirm_quota: Option<Decimal>,     // 确认额度
    pub used_quota: Option<Decimal>,        // 已使用额度
    pub is_delete: bool,
    pub desc: Option<String>, // 描述
    pub created_by: Option<String>,
    pub sign_time: Option<String>,           // 签订时间
    pub supplier_id: Option<String>,         // 供应商
    pub supplier_name: Option<String>,       // 供应商名称
    pub bid_winner_id: Option<String>,       // 中标方ID
    pub bid_winner_name: Option<String>,     // 中标方名称
    pub bid_owner_id: Option<String>,        // 招标方/终端
    pub bid_owner_name: Option<String>,      // 招标方/终端
    pub warehouse_id: Option<String>,        // 物流/仓储方
    pub warehouse_name: Option<String>,      // 物流/仓储方
    pub profit_calc_fee: Option<Decimal>,    // 利润计算费用
    pub penalty_calc_fee: Option<Decimal>,   // 违约金计算费用
    pub profit_calc_period: Option<String>,  // 计算周期
    pub penalty_calc_period: Option<String>, // 违约金计算方式
    pub product_category: Vec<String>,       // 商品目录，多个逗号分隔
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for FinancialContractUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct FinancialContractResponse {
    pub id: String,
    pub name: String,                  // 项目名称
    pub serial: String,                // 合同编号
    pub begin_time: Option<String>,    // 生效日期
    pub end_time: Option<String>,      // 失效时间
    pub applier: Option<String>,       // 申请人
    pub funder: Option<String>,        // 资金方
    pub scf_company: Option<String>,   // 运营商
    pub contract_type: Option<String>, // 合同类型
    pub status: String,                // 状态
    pub application_quota: Decimal,    // 合同金额(授信额度)
    pub confirm_quota: Decimal,        // 确认额度
    pub used_quota: Decimal,           // 已使用额度
    pub is_delete: bool,
    pub desc: Option<String>, // 描述
    pub created_by: Option<String>,
    pub sign_time: Option<String>,           // 签订时间
    pub supplier_id: Option<String>,         // 供应商
    pub supplier_name: Option<String>,       // 供应商名称
    pub bid_winner_id: Option<String>,       // 中标方ID
    pub bid_winner_name: Option<String>,     // 中标方名称
    pub bid_owner_id: Option<String>,        // 招标方/终端
    pub bid_owner_name: Option<String>,      // 招标方/终端
    pub warehouse_id: Option<String>,        // 物流/仓储方
    pub warehouse_name: Option<String>,      // 物流/仓储方
    pub profit_calc_fee: Decimal,            // 利润计算费用
    pub penalty_calc_fee: Decimal,           // 违约金计算费用
    pub profit_calc_period: Option<String>,  // 计算周期
    pub penalty_calc_period: Option<String>, // 违约金计算方式
    pub product_category: Vec<String>,       // 商品目录，多个逗号分隔
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for FinancialContractResponse {}
