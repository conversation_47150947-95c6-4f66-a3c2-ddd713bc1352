use crate::db::{Castable, Creatable, Patchable};
use crate::utils::date::{default_false, default_now, default_true};
use salvo::prelude::{Extractible, ToSchema};
use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Default, Deserialize, Clone, Serialize, Debug, Validate, ToSchema)]
pub struct UserCreate {
    pub username: String,
    #[validate(length(min = 6, message = "login_name length must be greater than 5"))]
    pub login_name: String,
    #[validate(length(min = 6, message = "login_pwd length must be greater than 5"))]
    pub login_pwd: String,
    pub stable: bool,
    pub serial: Option<String>,
    pub openid: Option<String>, // 微信openid
    pub avatar: Option<String>,
    pub email: Option<String>,
    pub mobile: Option<String>,
    #[serde(default = "default_false")]
    pub is_admin: bool,
    #[serde(default = "default_true")]
    pub is_active: bool,
    pub role_id: Option<String>,
    pub company_id: Option<String>,
    pub department_id: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Creatable for UserCreate {}

#[derive(Debug, Deserialize, Extractible, ToSchema, Clone)]
pub struct UserLoginRequest {
    pub username: String,
    pub password: String,
    pub captcha_str: String,
    pub captcha_id: String,
}
#[derive(Debug, Deserialize, ToSchema, Clone)]
pub enum AuthType {
    Wechat,
    Password,
    Qrcode,
    Mobile,
}

#[derive(Debug, Deserialize, Extractible, ToSchema, Clone)]
pub struct OpenLoginRequest {
    pub openid: String,
    pub secret: String,
    pub auth_type: AuthType,
}

#[derive(Default, Debug, Deserialize, Serialize, Extractible, ToSchema)]
// #[salvo(extract(default_source(from = "body", parse = "json")))]
pub struct UserUpdate {
    pub id: String,
    pub username: Option<String>,
    pub serial: Option<String>,
    pub login_name: Option<String>,
    pub login_pwd: Option<String>,
    pub openid: Option<String>, // 微信openid
    pub avatar: Option<String>,
    pub email: Option<String>,
    pub mobile: Option<String>,
    pub is_admin: Option<bool>,
    pub is_active: Option<bool>,
    pub role_id: Option<String>,
    pub company_id: Option<String>,
    pub department_id: Option<String>,
    #[serde(default = "default_now")]
    pub created_at: i64,
    #[serde(default = "default_now")]
    pub updated_at: i64,
}

impl Patchable for UserUpdate {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default, Clone)]
pub struct UserResponse {
    pub id: String,
    pub username: String,
    pub serial: String,
    pub login_name: String,
    pub login_pwd: String,
    pub openid: Option<String>, // 微信openid
    pub avatar: Option<String>,
    pub email: Option<String>,
    pub mobile: Option<String>,
    pub is_admin: bool,
    pub is_active: bool,
    pub role_id: Option<String>,
    pub company_id: Option<String>,
    pub department_id: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Castable for UserResponse {}

#[derive(Debug, Deserialize, Serialize, ToSchema, Default)]
pub struct UserLoginResponse {
    pub token: String,
    pub exp: i64,
}
