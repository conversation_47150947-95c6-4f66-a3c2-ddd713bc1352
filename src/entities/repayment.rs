use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        UpdateOptions, WhereOptions,
    },
    dtos::repayment::{RepaymentCreate, RepaymentResponse, RepaymentUpdate},
    entities::financial_contract::CalcPeriod,
    utils::rand_utils::random_uppercase_serial,
};
use anyhow::anyhow;
use chrono::{Datelike, Local, NaiveDate};
use rust_decimal::{prelude::*, Decimal};
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Repayment {
    pub id: Option<RecordId>,
    pub serial: Option<String>,                  // 还款计划编号
    pub contract_id: Option<RecordId>,           // 合同ID
    pub profit_amount: Option<String>,           // 需要偿还的利润
    pub principal_amount: Option<String>,        // 需要偿还的本金（从合同获取的额度）
    pub total_amount: Option<String>,            // 需要偿还的总额
    pub profit_remain: Option<String>,           // 剩余需要偿还的利润
    pub principal_remain: Option<String>,        // 剩余需要偿还的本金（从合同获取的额度）
    pub total_remain: Option<String>,            // 剩余需要偿还的总额
    pub target_amount: Option<String>,           // 选择的订单费用总额（订单总金额）
    pub profit_calc_fee: Decimal,                // 利润计算费用
    pub penalty_calc_fee: Decimal,               // 违约金计算费用
    pub profit_calc_period: Option<CalcPeriod>,  // 计算周期
    pub penalty_calc_period: Option<CalcPeriod>, // 违约金计算方式
    pub update_time: Option<i64>,                // 利息更新时间

    pub begin_date: Option<String>, // 额度支取的第一天，从这一天开始计算利润
    pub end_date: Option<String>,   // 约定偿还额度的最后一天，过了这一天就计算违约金
    pub grace_period: Option<String>, // 宽限期，过了这个日期才开始计算违约金
    pub repay_date: Option<String>, // 实际还款日期

    pub remark: Option<String>,     // 备注
    pub status: Option<String>,     // 状态
    pub creator_id: Option<String>, // 创建人ID
    pub updater_id: Option<String>, // 更新人ID
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for Repayment {}
impl Patchable for Repayment {}
impl Castable for Repayment {}

impl Repayment {
    /// 将 Repayment 实体转换为响应 DTO
    pub fn response(self) -> RepaymentResponse {
        RepaymentResponse {
            id: self.id.unwrap().to_string(),
            serial: self.serial,
            contract_id: self.contract_id.map(|id| id.to_string()),
            profit_amount: self.profit_amount,
            principal_amount: self.principal_amount,
            total_amount: self.total_amount,
            profit_remain: self.profit_remain,
            principal_remain: self.principal_remain,
            total_remain: self.total_remain,
            target_amount: self.target_amount,
            profit_calc_fee: self.profit_calc_fee,
            penalty_calc_fee: self.penalty_calc_fee,
            profit_calc_period: self.profit_calc_period.map(|p| format!("{:?}", p)),
            penalty_calc_period: self.penalty_calc_period.map(|p| format!("{:?}", p)),
            update_time: self.update_time,
            begin_date: self.begin_date,
            end_date: self.end_date,
            grace_period: self.grace_period,
            repay_date: self.repay_date,
            remark: self.remark,
            status: self.status,
            creator_id: self.creator_id,
            updater_id: self.updater_id,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    /// 从创建 DTO 创建新的 Repayment 实体
    pub fn create(obj: RepaymentCreate) -> Repayment {
        let time_now: i64 = Local::now().timestamp_millis();
        let contract_id = if obj.contract_id.is_some() {
            Some(RecordId::from_str(&obj.contract_id.unwrap()).unwrap())
        } else {
            None
        };
        Repayment {
            id: None,
            serial: obj.serial,
            contract_id: contract_id,
            profit_amount: obj.profit_amount,
            principal_amount: obj.principal_amount,
            total_amount: obj.total_amount,
            profit_remain: obj.profit_remain,
            principal_remain: obj.principal_remain,
            total_remain: obj.total_remain,
            target_amount: obj.target_amount,
            profit_calc_fee: obj.profit_calc_fee,
            penalty_calc_fee: obj.penalty_calc_fee,
            profit_calc_period: obj
                .profit_calc_period
                .and_then(|p| CalcPeriod::from_str(&p)),
            penalty_calc_period: obj
                .penalty_calc_period
                .and_then(|p| CalcPeriod::from_str(&p)),
            update_time: None,
            begin_date: obj.begin_date,
            end_date: obj.end_date,
            grace_period: obj.grace_period,
            repay_date: obj.repay_date,
            remark: obj.remark,
            status: obj.status, // 状态
            creator_id: obj.creator_id,
            updater_id: None, // 创建时不设置更新人ID
            created_at: time_now,
            updated_at: time_now,
        }
    }

    /// 从更新 DTO 更新 Repayment 实体
    pub fn update(mut self, obj: RepaymentUpdate) -> Repayment {
        let time_now: i64 = Local::now().timestamp_millis();

        // 只更新提供的字段
        if let Some(serial) = obj.serial {
            self.serial = Some(serial);
        }
        if let Some(contract_id) = obj.contract_id {
            self.contract_id = RecordId::from_str(&contract_id).ok();
        }
        if let Some(profit_amount) = obj.profit_amount {
            self.profit_amount = Some(profit_amount);
        }
        if let Some(principal_amount) = obj.principal_amount {
            self.principal_amount = Some(principal_amount);
        }
        if let Some(total_amount) = obj.total_amount {
            self.total_amount = Some(total_amount);
        }
        if let Some(profit_remain) = obj.profit_remain {
            self.profit_remain = Some(profit_remain);
        }
        if let Some(principal_remain) = obj.principal_remain {
            self.principal_remain = Some(principal_remain);
        }
        if let Some(total_remain) = obj.total_remain {
            self.total_remain = Some(total_remain);
        }
        if let Some(target_amount) = obj.target_amount {
            self.target_amount = Some(target_amount);
        }
        if let Some(profit_calc_fee) = obj.profit_calc_fee {
            self.profit_calc_fee = profit_calc_fee;
        }
        if let Some(penalty_calc_fee) = obj.penalty_calc_fee {
            self.penalty_calc_fee = penalty_calc_fee;
        }
        if let Some(profit_calc_period) = obj.profit_calc_period {
            self.profit_calc_period = CalcPeriod::from_str(&profit_calc_period);
        }
        if let Some(penalty_calc_period) = obj.penalty_calc_period {
            self.penalty_calc_period = CalcPeriod::from_str(&penalty_calc_period);
        }
        if let Some(begin_date) = obj.begin_date {
            self.begin_date = Some(begin_date);
        }
        if let Some(end_date) = obj.end_date {
            self.end_date = Some(end_date);
        }
        if let Some(grace_period) = obj.grace_period {
            self.grace_period = Some(grace_period);
        }
        if let Some(repay_date) = obj.repay_date {
            self.repay_date = Some(repay_date);
        }
        if let Some(remark) = obj.remark {
            self.remark = Some(remark);
        }
        if let Some(status) = obj.status {
            self.status = Some(status);
        }
        if let Some(updater_id) = obj.updater_id {
            self.updater_id = Some(updater_id);
        }
        if let Some(update_time) = obj.update_time {
            self.update_time = Some(update_time);
        }

        // 更新时间戳
        self.updated_at = time_now;

        self
    }
}

pub struct RepaymentBmc;

impl RepaymentBmc {
    const ENTITY: &'static str = "repayment";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<Repayment>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<Repayment>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    /// 根据ID获取还款记录（自动执行期间计算）
    ///
    /// # 参数
    /// * `id` - 还款记录ID
    ///
    /// # 返回值
    /// * `AppResult<Option<Repayment>>` - 操作结果，返回还款记录或None
    ///
    /// # 计算公式
    /// - 逾期利润 = 逾期天数 * 未偿还本金 * 逾期费率
    /// - 正常利润 = 正常天数 * 未偿还本金 * 正常费率
    /// - 还款金额 = 未偿还本金 + (正常利润 or 逾期利润)
    ///
    /// # 功能
    /// - 获取还款记录后，自动判断状态
    /// - 如果状态为 "processing"、"pending" 或 "partial"，自动执行期间计算
    /// - 将计算结果更新到数据库并返回更新后的记录
    /// - 其他状态直接返回原记录
    ///
    /// # 优化逻辑
    /// - 当should_calculate返回false时，不执行数据库更新操作，直接返回原记录
    pub async fn get_by_id(id: &str) -> AppResult<Option<Repayment>> {
        // 在函数开始时获取统一的时间戳，避免执行过程中时间切换
        let execution_timestamp = Local::now().timestamp_millis();

        // 先获取原始记录
        let repayment_opt: Option<Repayment> = Database::exec_get_by_id(Self::ENTITY, id).await?;

        if let Some(mut repayment) = repayment_opt {
            // 检查状态是否需要执行期间计算
            if let Some(ref status) = repayment.status {
                if status == "processing" || status == "pending" || status == "partial" {
                    // 执行期间计算，使用新的计算函数
                    match Self::cal_repayment_internal(&repayment, execution_timestamp).await {
                        Ok((profit_count, _calc_time, _is_penalty)) => {
                            // 检查是否有实际的利润计算结果
                            if profit_count == Decimal::ZERO {
                                // 利润为零说明不需要计算或没有变化，直接返回原记录
                                return Ok(Some(repayment));
                            }

                            // 累加计算结果到现有记录（增量更新）
                            // 逾期利润：更新 profit_amount（累加逾期利润）
                            let original_profit_amount =
                                if let Some(profit_str) = &repayment.profit_amount {
                                    Decimal::from_str(profit_str).unwrap_or(Decimal::ZERO)
                                } else {
                                    Decimal::ZERO
                                };
                            let new_profit_amount = original_profit_amount + profit_count;
                            repayment.profit_amount = Some(new_profit_amount.to_string());

                            let original_total_amount =
                                if let Some(total_str) = &repayment.total_amount {
                                    Decimal::from_str(total_str).unwrap_or(Decimal::ZERO)
                                } else {
                                    Decimal::ZERO
                                };
                            let new_total_amount = original_total_amount + profit_count;
                            repayment.total_amount = Some(new_total_amount.to_string());

                            let original_profit_remain =
                                if let Some(remain_str) = &repayment.profit_remain {
                                    Decimal::from_str(remain_str).unwrap_or(Decimal::ZERO)
                                } else {
                                    Decimal::ZERO
                                };
                            let new_profit_remain = original_profit_remain + profit_count;
                            repayment.profit_remain = Some(new_profit_remain.to_string());

                            let original_principal_remain =
                                if let Some(remain_str) = &repayment.principal_remain {
                                    Decimal::from_str(remain_str).unwrap_or(Decimal::ZERO)
                                } else {
                                    Decimal::ZERO
                                };
                            let new_principal_remain = original_principal_remain + profit_count;
                            repayment.principal_remain = Some(new_principal_remain.to_string());

                            let new_total_remain = new_profit_amount + new_principal_remain;
                            repayment.total_remain = Some(new_total_remain.to_string());

                            // 使用统一的时间戳更新，避免时间切换问题
                            repayment.updated_at = execution_timestamp;
                            repayment.update_time = Some(execution_timestamp);

                            // 获取记录ID用于更新
                            if let Some(record_id) = &repayment.id {
                                let tid = record_id.to_string();
                                let final_tid = if tid.contains(':') {
                                    tid.split(':').nth(1).unwrap_or(&tid).to_string()
                                } else {
                                    tid.clone()
                                };

                                // 执行数据库更新
                                match Database::exec_update(
                                    Self::ENTITY,
                                    &final_tid,
                                    repayment.clone(),
                                )
                                .await
                                {
                                    Ok(_) => {
                                        // 更新成功，返回更新后的记录
                                        return Ok(Some(repayment));
                                    }
                                    Err(e) => {
                                        // 更新失败，记录错误但仍返回计算后的记录
                                        eprintln!("Failed to update repayment after period calculation: {}", e);
                                        return Ok(Some(repayment));
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            // 计算失败，记录错误但仍返回原记录
                            eprintln!("Failed to calculate period for repayment {}: {}", id, e);
                        }
                    }
                }
            }

            // 状态不需要计算或计算失败，返回原记录
            Ok(Some(repayment))
        } else {
            // 记录不存在
            Ok(None)
        }
    }

    pub async fn create(mut repayment: RepaymentCreate) -> AppResult<String> {
        // 如果提供了 serial，检查是否重复
        if let Some(ref serial) = repayment.serial {
            let existing = Self::get_by_query(vec![WhereOptions::new(
                "serial".to_string(),
                serial.clone(),
            )])
            .await?;

            if existing.is_some() {
                return Err(anyhow!("还款计划编号 {} 已存在，请使用其他编号", serial).into());
            }
        } else {
            // 如果没有提供 serial，循环生成直到找到唯一的
            loop {
                let serial = random_uppercase_serial(Some("RP".to_string()), 6);
                let exists = Self::get_by_query(vec![WhereOptions::new(
                    "serial".to_string(),
                    serial.clone(),
                )])
                .await?
                .is_some();

                if !exists {
                    repayment.serial = Some(serial);
                    break;
                }
            }
        }

        let obj = Repayment::create(repayment);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(repayment: RepaymentUpdate) -> AppResult<String> {
        println!("更新的请求repayment: {:?}", repayment);
        let check: Option<Repayment> =
            Database::exec_get_by_id(Self::ENTITY, &repayment.id.clone().to_string()).await?;
        if check.is_none() {
            return Err(anyhow!("Repayment not found.").into());
        }
        let old = check.unwrap();
        let tid = old.id.clone().unwrap().to_string();
        let final_tid = if tid.contains(':') {
            tid.split(':').nth(1).unwrap_or(&tid).to_string()
        } else {
            tid.clone()
        };
        let obj = Repayment::update(old, repayment);
        println!("更新的结果obj: {:?}", obj);
        Database::exec_update(Self::ENTITY, &final_tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        let check: Option<Repayment> = Database::exec_get_by_id(Self::ENTITY, &id).await?;
        if check.is_none() {
            return Err(anyhow!("Repayment not found.").into());
        }
        let check = check.unwrap();
        // 检查还款计划状态，只有草稿或新建状态才能删除
        if let Some(status) = &check.status {
            if status != "draft" && status != "new" {
                return Err(anyhow!("Cannot delete completed repayment.").into());
            }
        }
        if check.status.as_deref() != Some("draft") || check.status.as_deref() != Some("new") {
            return Err(anyhow!("Cannot delete completed repayment.").into());
        }
        // 删除还款计划前检查状态
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }

    pub async fn unrelate_to_item(table: &str, from: &str, to: &str) -> AppResult<String> {
        Database::exec_unrelate(table, from, to).await
    }

    /// 根据查询条件更新单个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_field` - 要更新的字段名
    /// * `update_value` - 要更新的字段值
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "RP001".to_string())];
    /// let result = RepaymentBmc::update_field(params, "status", "completed").await?;
    /// ```
    pub async fn update_field(
        t_id: &str,
        update_field: &str,
        update_value: &str,
    ) -> AppResult<String> {
        Database::exec_update_by_id(t_id, update_field, update_value).await
    }

    /// 根据查询条件更新多个字段
    ///
    /// # 参数
    /// * `params` - 查询条件
    /// * `update_fields` - 要更新的字段列表
    ///
    /// # 返回值
    /// * `AppResult<String>` - 操作结果，返回更新记录的ID
    ///
    /// # 示例
    /// ```rust
    /// let params = vec![WhereOptions::new("serial".to_string(), "SO001".to_string())];
    /// let update_fields = vec![
    ///     UpdateOptions::new("status".to_string(), "completed".to_string()),
    ///     UpdateOptions::new("amount".to_string(), "1000.00".to_string()),
    /// ];
    /// let result = SalesOrderBmc::update_multiple_fields(params, update_fields).await?;
    /// ```
    pub async fn update_multiple_fields(
        params: Vec<WhereOptions>,
        update_fields: Vec<UpdateOptions>,
    ) -> AppResult<String> {
        Database::exec_update_multiple_fields_by_query(Self::ENTITY, params, update_fields).await
    }

    /// 计算还款计划的利润
    ///
    /// # 参数
    /// * `repayment_id` - 还款计划ID
    ///
    /// # 返回值
    /// * `AppResult<(Decimal, i64, bool)>` - 返回 (利润金额, 计算时间戳, 是否为逾期利润)
    ///
    /// # 计算规则
    /// 1. 统计周期根据 profit_calc_period 进行计算
    /// 2. 按日统计差值至少为1天，按月统计差值为30天
    /// 3. begin_date + 1 是显示统计正常利润结果的第一天
    /// 4. end_date + 1 (或 end_date + grace_period + 1) 是显示统计正常利润结果的最后一天
    /// 5. 正常利润：profit = principal_remain * (转换为日利率的profit_calc_fee) * 时间差
    /// 6. 逾期利润：profit = principal_remain * (转换为日利率的penalty_calc_fee) * 时间差
    pub async fn cal_repayment(repayment_id: &str) -> AppResult<(Decimal, i64, bool)> {
        // 获取还款计划信息
        let repayment: Option<Repayment> =
            Database::exec_get_by_id(Self::ENTITY, &repayment_id).await?;

        if repayment.is_none() {
            return Err(anyhow!("Repayment not found: {}", repayment_id).into());
        }
        let repayment = repayment.unwrap();

        // 生成计算时间戳
        let calc_time = Local::now().timestamp_millis();

        Self::cal_repayment_internal(&repayment, calc_time).await
    }

    /// 内部计算函数
    ///
    /// # 参数
    /// * `repayment` - 还款计划对象
    /// * `calc_time` - 计算时间戳
    ///
    /// # 返回值
    /// * `AppResult<(Decimal, i64, bool)>` - 返回 (利润金额, 计算时间戳, 是否为逾期利润)
    async fn cal_repayment_internal(
        repayment: &Repayment,
        calc_time: i64,
    ) -> AppResult<(Decimal, i64, bool)> {
        // 将时间戳转换为日期
        let current_date = match chrono::DateTime::from_timestamp_millis(calc_time) {
            Some(dt) => dt.date_naive(),
            None => {
                return Err(anyhow!("Invalid calc_time timestamp: {}", calc_time).into());
            }
        };

        // 获取剩余总额作为计算基数
        let principal_remain = if let Some(principal_str) = &repayment.principal_remain {
            Decimal::from_str(principal_str).unwrap_or(Decimal::ZERO)
        } else {
            Decimal::ZERO
        };

        // 如果没有剩余总额，直接返回零值
        if principal_remain == Decimal::ZERO {
            return Ok((Decimal::ZERO, calc_time, false));
        }

        // 检查是否需要进行统计（基于统计周期和update_time）
        let should_calc = Self::should_calculate(repayment, current_date)?;

        if !should_calc {
            return Ok((Decimal::ZERO, calc_time, false));
        }

        // 判断是正常利润还是逾期利润
        let is_penalty = Self::is_penalty_period(repayment, current_date)?;

        // 计算利润
        let profit = if is_penalty {
            Self::calculate_penalty_profit(repayment, principal_remain, current_date)?
        } else {
            Self::calculate_normal_profit(repayment, principal_remain, current_date)?
        };

        Ok((profit, calc_time, is_penalty))
    }

    /// 检查是否需要进行统计
    fn should_calculate(repayment: &Repayment, current_date: NaiveDate) -> AppResult<bool> {
        // 如果没有update_time，需要统计
        let update_time = match repayment.update_time {
            Some(time) => time,
            None => return Ok(true),
        };

        // 将update_time转换为日期
        let update_date = chrono::DateTime::from_timestamp_millis(update_time)
            .ok_or_else(|| anyhow!("Invalid update_time timestamp: {}", update_time))?
            .date_naive();

        // 根据统计周期判断是否需要统计
        match &repayment.profit_calc_period {
            Some(CalcPeriod::DAY) => {
                // 按日统计，差值至少为1天
                Ok(current_date.signed_duration_since(update_date).num_days() >= 1)
            }
            Some(CalcPeriod::MONTH) => {
                // 按月统计，差值为30天
                Ok(current_date.signed_duration_since(update_date).num_days() >= 30)
            }
            _ => Ok(true), // 其他情况默认需要统计
        }
    }

    /// 判断是否为逾期利润统计期
    fn is_penalty_period(repayment: &Repayment, current_date: NaiveDate) -> AppResult<bool> {
        // 获取约定还款日期
        let end_date = if let Some(end_str) = &repayment.end_date {
            match NaiveDate::parse_from_str(end_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => return Err(anyhow!("Invalid end_date format: {}", end_str).into()),
            }
        } else {
            return Ok(false); // 没有约定还款日期，不算逾期
        };

        // 计算实际的逾期开始日期（考虑宽限期）
        let penalty_start_date = if let Some(grace_str) = &repayment.grace_period {
            // 解析宽限期天数
            let grace_days: i64 = grace_str
                .parse()
                .map_err(|_| anyhow!("Invalid grace_period format: {}", grace_str))?;
            end_date + chrono::Duration::days(grace_days + 1)
        } else {
            end_date + chrono::Duration::days(1)
        };

        // 如果当前日期大于逾期开始日期，则为逾期期间
        Ok(current_date > penalty_start_date)
    }

    /// 计算正常利润
    fn calculate_normal_profit(
        repayment: &Repayment,
        principal_remain: Decimal,
        current_date: NaiveDate,
    ) -> AppResult<Decimal> {
        // 获取开始日期
        let begin_date = if let Some(begin_str) = &repayment.begin_date {
            match NaiveDate::parse_from_str(begin_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => return Err(anyhow!("Invalid begin_date format: {}", begin_str).into()),
            }
        } else {
            return Ok(Decimal::ZERO);
        };

        // 计算统计开始日期：begin_date + 1
        let profit_start_date = begin_date + chrono::Duration::days(1);

        // 获取约定还款日期
        let end_date = if let Some(end_str) = &repayment.end_date {
            match NaiveDate::parse_from_str(end_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => return Err(anyhow!("Invalid end_date format: {}", end_str).into()),
            }
        } else {
            return Ok(Decimal::ZERO);
        };

        // 计算统计结束日期：end_date + grace_period + 1（如果有宽限期）
        let profit_end_date = if let Some(grace_str) = &repayment.grace_period {
            let grace_days: i64 = grace_str
                .parse()
                .map_err(|_| anyhow!("Invalid grace_period format: {}", grace_str))?;
            end_date + chrono::Duration::days(grace_days + 1)
        } else {
            end_date + chrono::Duration::days(1)
        };

        // 检查当前日期是否在正常利润统计范围内
        if current_date < profit_start_date || current_date > profit_end_date {
            return Ok(Decimal::ZERO);
        }

        // 计算时间差（从profit_start_date到current_date）
        let days_diff = current_date
            .signed_duration_since(profit_start_date)
            .num_days();
        if days_diff <= 0 {
            return Ok(Decimal::ZERO);
        }

        // 将年化利率转换为日利率
        let daily_rate = repayment.profit_calc_fee / Decimal::from(365);

        // 计算利润：total_remain * 日利率 * 时间差
        Ok(principal_remain * daily_rate * Decimal::from(days_diff))
    }

    /// 计算逾期利润
    fn calculate_penalty_profit(
        repayment: &Repayment,
        principal_remain: Decimal,
        current_date: NaiveDate,
    ) -> AppResult<Decimal> {
        // 获取约定还款日期
        let end_date = if let Some(end_str) = &repayment.end_date {
            match NaiveDate::parse_from_str(end_str, "%Y-%m-%d") {
                Ok(date) => date,
                Err(_) => return Err(anyhow!("Invalid end_date format: {}", end_str).into()),
            }
        } else {
            return Ok(Decimal::ZERO);
        };

        // 计算逾期开始日期：end_date + grace_period + 1（如果有宽限期）
        let penalty_start_date = if let Some(grace_str) = &repayment.grace_period {
            let grace_days: i64 = grace_str
                .parse()
                .map_err(|_| anyhow!("Invalid grace_period format: {}", grace_str))?;
            end_date + chrono::Duration::days(grace_days + 1)
        } else {
            end_date + chrono::Duration::days(1)
        };

        // 检查当前日期是否在逾期期间
        if current_date <= penalty_start_date {
            return Ok(Decimal::ZERO);
        }

        // 计算逾期天数
        let overdue_days = current_date
            .signed_duration_since(penalty_start_date)
            .num_days();
        if overdue_days <= 0 {
            return Ok(Decimal::ZERO);
        }

        // penalty_calc_fee 本身就是日利率，不需要转换
        let daily_penalty_rate = repayment.penalty_calc_fee;

        // 计算逾期利润：total_remain * penalty_calc_fee * 时间差
        Ok(principal_remain * daily_penalty_rate * Decimal::from(overdue_days))
    }
}
