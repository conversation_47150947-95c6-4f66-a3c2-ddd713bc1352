use crate::{
    app_writer::AppResult,
    db::{CountRecord, Creatable, ListOptions, Patchable},
    dtos::user::{UserCreate, UserResponse, UserUpdate},
    utils::rand_utils::{self, random_uppercase_serial},
};
use std::str::FromStr;

use crate::db::{Castable, Database, WhereOptions};
use anyhow::anyhow;
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct User {
    pub id: Option<RecordId>,
    pub serial: Option<String>,
    pub login_name: String,
    pub login_pwd: String,
    pub username: String,
    pub stable: bool,
    pub openid: Option<String>, // 微信openid
    pub avatar: Option<String>,
    pub email: Option<String>,
    pub mobile: Option<String>,
    pub is_admin: bool,
    pub is_active: bool,
    pub role_id: Option<RecordId>,
    pub company_id: Option<RecordId>,
    pub department_id: Option<RecordId>,
    pub created_at: i64,
    pub updated_at: i64,
}

impl User {
    pub fn response(self) -> UserResponse {
        let role_id = if self.role_id.is_none() {
            String::from("")
        } else {
            self.role_id.unwrap().to_string()
        };
        let company_id = if self.company_id.is_none() {
            None
        } else {
            Some(self.company_id.unwrap().to_string())
        };
        let department_id = if self.department_id.is_none() {
            None
        } else {
            Some(self.department_id.unwrap().to_string())
        };
        UserResponse {
            id: self.id.unwrap().to_string(),
            username: self.username,
            serial: self.serial.unwrap_or_default(),
            login_name: self.login_name,
            login_pwd: self.login_pwd,
            openid: self.openid,
            avatar: self.avatar,
            email: self.email,
            mobile: self.mobile,
            is_admin: self.is_admin,
            is_active: self.is_active,
            role_id: Some(role_id),
            company_id,
            department_id,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }

    pub async fn create(user: UserCreate) -> User {
        let role_id = if user.role_id.is_none() {
            None
        } else {
            Some(RecordId::from_str(&user.role_id.unwrap()).unwrap())
        };

        let company_id = if user.company_id.is_none() {
            None
        } else {
            Some(RecordId::from_str(&user.company_id.unwrap()).unwrap())
        };

        let department_id = if user.department_id.is_none() {
            None
        } else {
            Some(RecordId::from_str(&user.department_id.unwrap()).unwrap())
        };

        // 循环生成 serial 直到找到唯一的
        let serial = loop {
            let serial = random_uppercase_serial(None, 8);
            let exists = UserBmc::get_by_query(vec![WhereOptions::new(
                "serial".to_string(),
                serial.clone(),
            )])
            .await
            .unwrap()
            .is_some();
            if !exists {
                break serial;
            }
        };

        let time_now = Local::now().timestamp_millis();
        User {
            id: None,
            username: user.username,
            login_name: user.login_name,
            login_pwd: user.login_pwd,
            serial: Some(serial),
            stable: user.stable,
            openid: user.openid,
            avatar: user.avatar,
            email: user.email,
            mobile: user.mobile,
            is_admin: user.is_admin,
            is_active: user.is_active,
            role_id,
            company_id,
            department_id,
            created_at: time_now,
            updated_at: time_now,
        }
    }

    pub fn update(new: UserUpdate, old: User) -> User {
        let username = if new.username.is_some() {
            new.username.unwrap()
        } else {
            old.username
        };
        let login_name = if new.login_name.is_some() {
            new.login_name.unwrap()
        } else {
            old.login_name
        };
        let login_pwd = if new.login_pwd.is_some() {
            new.login_pwd.unwrap()
        } else {
            old.login_pwd
        };
        let role_id = if new.role_id.is_none() {
            old.role_id
        } else {
            Some(RecordId::from_str(&new.role_id.unwrap()).unwrap())
        };
        let company_id = if new.company_id.is_none() {
            old.company_id
        } else {
            Some(RecordId::from_str(&new.company_id.unwrap()).unwrap())
        };
        let department_id = if new.department_id.is_none() {
            old.department_id
        } else {
            Some(RecordId::from_str(&new.department_id.unwrap()).unwrap())
        };
        let time_now = Local::now().timestamp_millis();
        let openid = if new.openid.is_some() {
            new.openid
        } else {
            old.openid
        };
        let avatar = if new.avatar.is_some() {
            new.avatar
        } else {
            old.avatar
        };
        let email = if new.email.is_some() {
            new.email
        } else {
            old.email
        };
        let mobile = if new.mobile.is_some() {
            new.mobile
        } else {
            old.mobile
        };
        let is_admin = if new.is_admin.is_some() {
            new.is_admin.unwrap()
        } else {
            old.is_admin
        };
        let is_active = if new.is_active.is_some() {
            new.is_active.unwrap()
        } else {
            old.is_active
        };
        User {
            id: None,
            username,
            login_name,
            login_pwd,
            stable: old.stable,
            serial: old.serial,
            openid,
            avatar,
            email,
            mobile,
            is_admin,
            is_active,
            role_id,
            company_id,
            department_id,
            created_at: old.created_at,
            updated_at: time_now,
        }
    }
}

impl Creatable for User {}
impl Castable for User {}
impl Patchable for User {}

pub struct UserBmc;

impl UserBmc {
    const ENTITY: &'static str = "user";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<User>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<User>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<User>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(user: UserCreate) -> AppResult<String> {
        let obj = User::create(user).await;
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(user: UserUpdate) -> AppResult<String> {
        let check = UserBmc::get_by_id(&user.id.clone()).await?;
        if check.is_none() {
            return Err(anyhow!("用户不存在。").into());
        }
        let old = check.unwrap();
        let tid = user.id.clone();
        let obj = User::update(user, old);
        Database::exec_update(Self::ENTITY, &tid, obj).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn auth_user(login_name: String, login_pwd: String) -> AppResult<Option<User>> {
        let params = vec![WhereOptions::new("login_name".to_string(), login_name)];
        let user: Option<User> = Database::exec_get_by_query(Self::ENTITY, params).await?;
        if user.is_none() {
            return Ok(None);
        }
        let user = user.unwrap();
        if rand_utils::verify_password(login_pwd, user.clone().login_pwd)
            .await
            .is_err()
        {
            return Err(anyhow!("密码不正确。").into());
        }
        Ok(Some(user))
    }
}
