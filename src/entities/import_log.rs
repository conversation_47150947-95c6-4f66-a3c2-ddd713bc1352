use std::str::FromStr;

use crate::{
    app_writer::AppResult,
    db::{
        Castable, CountRecord, Creatable, Database, ListOptions, Patchable, RelateParams,
        WhereOptions,
    },
    dtos::import_log::{ImportLogCreate, ImportLogResponse, ImportLogUpdate},
};
use chrono::Local;
use serde::{Deserialize, Serialize};
use surrealdb::RecordId;

#[derive(Debug, Serialize, Deserialize)]
pub struct ImportLog {
    pub id: Option<RecordId>,
    pub import_id: String,     // 项目ID
    pub import_source: String, // 获取数据的来源
    pub import_err: String,
    pub created_at: i64,
    pub updated_at: i64,
}

impl Creatable for ImportLog {}
impl Patchable for ImportLog {}
impl Castable for ImportLog {}

impl ImportLog {
    pub fn response(self) -> ImportLogResponse {
        ImportLogResponse {
            id: self.id.unwrap().to_string(),
            import_id: self.import_id,
            import_source: self.import_source,
            import_err: self.import_err,
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
    pub fn create(obj: ImportLogCreate) -> ImportLog {
        let time_now = Local::now().timestamp_millis();
        ImportLog {
            id: None,
            import_id: obj.import_id,
            import_source: obj.import_source,
            import_err: obj.import_err,
            created_at: obj.created_at,
            updated_at: obj.updated_at,
        }
    }
}

pub struct ImportLogBmc;

impl ImportLogBmc {
    const ENTITY: &'static str = "import_log";

    pub async fn get_list(
        page: u32,
        limit: u32,
        options: Option<ListOptions>,
        params: Vec<WhereOptions>,
    ) -> AppResult<Vec<ImportLog>> {
        Database::exec_query_list(Self::ENTITY, page, limit, options, params).await
    }

    pub async fn get_total(params: Vec<WhereOptions>) -> AppResult<Option<CountRecord>> {
        Database::exec_query_count(Self::ENTITY, params).await
    }

    pub async fn get_by_query(params: Vec<WhereOptions>) -> AppResult<Option<ImportLog>> {
        Database::exec_get_by_query(Self::ENTITY, params).await
    }

    pub async fn get_by_id(id: &str) -> AppResult<Option<ImportLog>> {
        Database::exec_get_by_id(Self::ENTITY, id).await
    }

    pub async fn create(import_log: ImportLogCreate) -> AppResult<String> {
        let obj = ImportLog::create(import_log);
        Database::exec_create(Self::ENTITY, obj).await
    }

    pub async fn update(import_log: ImportLogUpdate) -> AppResult<String> {
        let tid = import_log.id.clone();
        Database::exec_update(Self::ENTITY, &tid.to_string(), import_log).await
    }

    pub async fn delete(id: String) -> AppResult<String> {
        Database::exec_delete(Self::ENTITY, id).await
    }

    pub async fn relate_to_item(params: RelateParams, state: &str) -> AppResult<String> {
        let from = vec![params.from];
        Database::exec_relate_batch(state, from, params.to).await
    }
}
