# 系统菜单配置
menus:
  - name: "contractList"
    order: 1
    path: "/contract/list"
    component: "pages/contract/index"
    redirect: ""
    active: "yes"
    title: "ContractList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "服务订单列表"
  - name: "contractDetail"
    order: 1
    path: "/contract/detail"
    component: "pages/contract/detail"
    redirect: ""
    active: "yes"
    title: "ContractDetail"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "contractList"
    remark: "合约详情"
  - name: "contractCreate"
    order: 2
    path: "/contract/create"
    component: "pages/contract/create"
    redirect: ""
    active: "yes"
    title: "ContractCreate"
    icon: "note_add"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "contractList"
    remark: ""
  - name: "contractEdit"
    order: 3
    path: "/contract/edit"
    component: "pages/contract/edit"
    redirect: ""
    active: "yes"
    title: "ContractEdit"
    icon: "edit_note"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "contractList"
    remark: ""
  - name: "orderList"
    order: 2
    path: "/order/list"
    component: "pages/order/index"
    redirect: ""
    active: "yes"
    title: "OrderList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "服务订单列表"
  - name: "orderDetail"
    order: 1
    path: "/order/detail"
    component: "pages/order/detail"
    redirect: ""
    active: "yes"
    title: "orderDetail"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "orderList"
    remark: "订单详情"
  - name: "categoryList"
    order: 3
    path: "/category/list"
    component: "pages/category/index"
    redirect: ""
    active: "yes"
    title: "CategoryList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "文章目录"
  - name: "productSkuList"
    order: 4
    path: "/productSku/list"
    component: "pages/productSku/index"
    redirect: ""
    active: "yes"
    title: "ProductSkuList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "产品管理"
  - name: "productSkuDetail"
    order: 1
    path: "/productSku/detail"
    component: "pages/productSku/detail"
    redirect: ""
    active: "yes"
    title: "ProductSkuDetail"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "productSkuList"
    remark: "产品详情"
  - name: "supplierList"
    order: 5
    path: "/supplier/list"
    component: "pages/supplier/index"
    redirect: ""
    active: "yes"
    title: "SupplierList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "供应商列表"
  - name: "warehouseList"
    order: 6
    path: "/warehouse/list"
    component: "pages/warehouse/index"
    redirect: ""
    active: "yes"
    title: "WarehouseList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "仓库列表"
  - name: "warehouseDetail"
    order: 1
    path: "/warehouse/detail"
    component: "pages/warehouse/detail"
    redirect: ""
    active: "yes"
    title: "WarehouseDetail"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "WarehouseList"
    remark: "仓库详情"
  - name: "stockList"
    order: 7
    path: "/stock/list"
    component: "pages/stock/index"
    redirect: ""
    active: "yes"
    title: "StockList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "库存列表"
  - name: "stockDetail"
    order: 1
    path: "/stock/detail"
    component: "pages/stock/detail"
    redirect: ""
    active: "yes"
    title: "StockDetail"
    icon: "group"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "StockList"
    remark: "库存详情"
  - name: "importRecord"
    order: 8
    path: "/import_recod/list"
    component: "pages/importRecord/index"
    redirect: ""
    active: "yes"
    title: "ImportRecord"
    icon: "import_export"
    keep_alive: "yes"
    hidden: "no"
    is_link: "no"
    parent: ""
    remark: ""
  - name: "repaymentDetail"
    order: 9
    path: "/repayment/detail"
    component: "pages/contract/repaymentDetail"
    redirect: ""
    active: "yes"
    title: "RepaymentDetail"
    icon: "add"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "contractList"
    remark: ""
  - name: "companyList"
    order: 95
    path: "/company/list"
    component: "pages/company/index"
    redirect: ""
    active: "yes"
    title: "CompanyList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "企业列表"
  - name: "companyCreate"
    order: 1
    path: "/company/create"
    component: "pages/company/create"
    redirect: ""
    active: "yes"
    title: "CompanyCreate"
    icon: "123"
    keep_alive: "yes"
    hidden: "yes"
    is_link: "no"
    parent: "companyList"
    remark: ""
  - name: "menuList"
    order: 96
    path: "/menu/list"
    component: "pages/menu/index"
    redirect: ""
    active: "yes"
    title: "MenuList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "菜单列表"
  - name: "permissionList"
    order: 97
    path: "/permission/list"
    component: "pages/permission/index"
    redirect: ""
    active: "yes"
    title: "PermissionList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "权限列表"
  - name: "roleList"
    order: 98
    path: "/role/list"
    component: "pages/role/index"
    redirect: ""
    active: "yes"
    title: "RoleList"
    icon: "group"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "角色列表"
  - name: "userList"
    order: 99
    path: "/user/list"
    component: "pages/user/index"
    redirect: ""
    active: "yes"
    title: "UserList"
    icon: "123"
    keep_alive: "no"
    hidden: "no"
    is_link: "no"
    parent: "''"
    remark: "用户列表"
  - name: "userDetail"
    order: 1
    path: "/user/detail"
    component: "pages/user/detail"
    redirect: ""
    active: "yes"
    title: "UserDetail"
    icon: "123"
    keep_alive: "yes"
    hidden: ""
    is_link: "no"
    parent: "userList"
    remark: "用户详情"
